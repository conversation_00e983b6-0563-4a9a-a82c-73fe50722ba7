import { Test, TestingModule } from '@nestjs/testing';
import { StorageService } from '../src/storage/storage.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';

describe('StorageService (Integration)', () => {
  let storageService: StorageService;
  let configService: ConfigService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env', // Make sure you have a .env file with your variables
        }),
      ],
      providers: [StorageService],
    }).compile();

    storageService = module.get<StorageService>(StorageService);
    configService = module.get<ConfigService>(ConfigService);

    // Verify that necessary config is loaded
    const bucketName = configService.get('GOOGLE_CLOUD_STORAGE_BUCKET');
    if (!bucketName) {
      throw new Error('GOOGLE_CLOUD_STORAGE_BUCKET is not defined in the environment variables. Please check your .env file or environment configuration.');
    }
  });

  it('should generate a signed URL and allow file upload', async () => {
    // 1. Define file details
    const contentType = 'image/png';
    const objectPath = `test-uploads/lightBackground-test-${Date.now()}.png`;
    const imagePath = path.resolve(
      __dirname,
      '../../ChroniCare/assets/images/lightBackground.png',
    );

    // Verify the image file exists
    expect(fs.existsSync(imagePath)).toBe(true);

    // 2. Generate signed URL
    const signedUrl = await storageService.generateUploadUrl(
      objectPath,
      contentType,
    );
    expect(signedUrl).toBeDefined();
    expect(typeof signedUrl).toBe('string');

    // 3. Read the image file
    const imageBuffer = fs.readFileSync(imagePath);

    // 4. Upload the file using the signed URL
    let uploadResponse;
    try {
      uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        body: imageBuffer,
        headers: {
          'Content-Type': contentType,
        },
      });
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
    
    // 5. Assert the upload was successful
    expect(uploadResponse.status).toBe(200);

    // Optional: Verify the public URL
    const publicUrl = storageService.getPublicUrl(objectPath);
    console.log('File uploaded successfully. Public URL:', publicUrl);
  });
});
