# NestJS Backend with GraphQL, Prisma, Firestore & JWT

A modern NestJS backend application with GraphQL API, PostgreSQL database via Prisma, Google Firestore integration, and JWT authentication.

## Features

- 🚀 **NestJS Framework** - Scalable Node.js server-side applications
- 🎯 **GraphQL API** - Type-safe API with Apollo Server
- 🗄️ **PostgreSQL Database** - Relational database with Prisma ORM
- 🔥 **Google Firestore** - NoSQL document database integration
- 🔐 **JWT Authentication** - Secure authentication with Passport
- ✅ **Input Validation** - Request validation with class-validator
- 🎨 **TypeScript** - Full TypeScript support

## Prerequisites

- Node.js (v18 or higher)
- PostgreSQL database
- Google Cloud Platform account with Firestore enabled
- Service account key for GCP

## Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment variables:**
   
   Update the `.env` file with your actual values:
   ```env
   # Database
   DATABASE_URL="postgresql://username:password@localhost:5432/your_database_name?schema=public"
   
   # JWT
   JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
   JWT_EXPIRES_IN="7d"
   
   # Google Cloud Platform
   GOOGLE_APPLICATION_CREDENTIALS="path/to/your/service-account-key.json"
   FIRESTORE_PROJECT_ID="your-gcp-project-id"
   
   # App Configuration
   PORT=3000
   NODE_ENV="development"
   ```

3. **Set up the database:**
   ```bash
   # Generate Prisma client
   npx prisma generate
   
   # Push the schema to your database
   npx prisma db push
   
   # (Optional) Seed the database
   npx prisma db seed
   ```

## Running the Application

### Development
```bash
npm run start:dev
```

### Production
```bash
npm run build
npm run start:prod
```

The application will be available at:
- **API**: http://localhost:3000
- **GraphQL Playground**: http://localhost:3000/graphql

## API Documentation

### Authentication

#### Register a new user
```graphql
mutation {
  register(registerInput: {
    email: "<EMAIL>"
    password: "password123"
    firstName: "John"
    lastName: "Doe"
  }) {
    access_token
    user {
      id
      email
      firstName
      lastName
    }
  }
}
```

#### Login
```graphql
mutation {
  login(loginInput: {
    email: "<EMAIL>"
    password: "password123"
  }) {
    access_token
    user {
      id
      email
      firstName
      lastName
    }
  }
}
```

### Users (Protected Routes)

#### Get all users
```graphql
query {
  users {
    id
    email
    firstName
    lastName
    createdAt
    updatedAt
  }
}
```

#### Get user by ID
```graphql
query {
  user(id: "user-id") {
    id
    email
    firstName
    lastName
  }
}
```

#### Update user
```graphql
mutation {
  updateUser(
    id: "user-id"
    updateUserInput: {
      firstName: "Updated Name"
    }
  ) {
    id
    firstName
  }
}
```

#### Delete user
```graphql
mutation {
  deleteUser(id: "user-id") {
    id
    email
  }
}
```

## Authentication

To access protected routes, include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Database Schema

The application includes a basic User model:

```prisma
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String?
  lastName  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}
```

## Project Structure

```
src/
├── auth/                 # Authentication module
│   ├── dto/             # Data transfer objects
│   ├── auth.module.ts   # Auth module
│   ├── auth.service.ts  # Auth business logic
│   ├── auth.resolver.ts # GraphQL resolvers
│   ├── jwt.strategy.ts  # JWT strategy
│   └── jwt-auth.guard.ts # JWT guard
├── firestore/           # Firestore integration
│   ├── firestore.module.ts
│   └── firestore.service.ts
├── prisma/              # Database module
│   ├── prisma.module.ts
│   └── prisma.service.ts
├── users/               # Users module
│   ├── dto/             # Data transfer objects
│   ├── user.entity.ts   # User GraphQL entity
│   ├── users.module.ts  # Users module
│   ├── users.service.ts # Users business logic
│   └── users.resolver.ts # GraphQL resolvers
├── app.module.ts        # Root module
└── main.ts              # Application entry point
```

## Available Scripts

- `npm run start` - Start the application
- `npm run start:dev` - Start in development mode with hot reload
- `npm run start:prod` - Start in production mode
- `npm run build` - Build the application
- `npm run test` - Run tests
- `npm run test:e2e` - Run end-to-end tests
- `npx prisma studio` - Open Prisma Studio (database GUI)
- `npx prisma migrate dev` - Create and apply migrations
- `npx prisma generate` - Generate Prisma client

## Environment Setup

### PostgreSQL Setup
1. Install PostgreSQL locally or use a cloud service
2. Create a new database
3. Update the `DATABASE_URL` in your `.env` file

### Google Cloud Firestore Setup
1. Create a GCP project
2. Enable Firestore API
3. Create a service account and download the JSON key
4. Set the path to the key file in `GOOGLE_APPLICATION_CREDENTIALS`
5. Update `FIRESTORE_PROJECT_ID` with your project ID

## Security Notes

- Change the `JWT_SECRET` to a strong, random string in production
- Use environment-specific `.env` files
- Never commit sensitive credentials to version control
- Use HTTPS in production
- Implement rate limiting for production use

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
