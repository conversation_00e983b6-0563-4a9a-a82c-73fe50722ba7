import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { User, UserPseudonym } from './user.entity';
import { CreateUserInput } from './dto/create-user.input';
import * as admin from 'firebase-admin';

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async checkUserExistsByEmail(email: string): Promise<boolean> {
    // It uses Prisma to query the 'user' table in the database
    console.log('Checking user existence for email:', email);
    const user = await this.prisma.user.findUnique({ 
      where: { email }, // Looks for a unique user by their email
      select: { id: true }, // Optimization: only fetch the 'id' if the user exists
    });
    return !!user; // Converts the result (user object or null) to a boolean
  }

  async findAll(): Promise<User[]> {
    console.log('Finding all users');
    return this.prisma.user.findMany(); 
  }
 
  async findOne(id: string): Promise<User | null> {
    console.log('Finding user by id:', id);
    return this.prisma.user.findUnique({
      where: { id },
    });
  }

  async findByEmail(email: string): Promise<User | null> {
    console.log('Finding user by email:', email);
    return this.prisma.user.findUnique({
      where: { email },
    });
  }

  async findByFirebaseUid(firebaseUid: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { firebaseUid },
    });
  }

  async upsertUser(input: CreateUserInput): Promise<User> {
    console.log('Upserting user with input:', input);
    
    const userData = {
      firebaseUid: input.firebaseUid,
      email: input.email,
      displayName: input.displayName || null,
      emailVerified: input.emailVerified ?? false,
      photoURL: input.photoURL || null,
      onboardingCompleted: input.onboardingCompleted ?? false,
      lastSignInTime: new Date(),
    };

    try {
      const version = process.env.CONSENT_VERSION || 'v1.0';
      
      const user = await this.prisma.user.upsert({
        where: { firebaseUid: input.firebaseUid },
        update: {
          ...userData,
          updatedAt: new Date(),
        },
        create: {
          ...userData,
          creationTime: new Date(),
          consents: {
            create: [
              {
                type: 'TERMS_OF_SERVICE',
                version,
                granted: true,
                timestamp: new Date(),
              },
            ],
          },
        },
      });

      console.log('User upserted successfully:', user.firebaseUid);
      
      // Create pseudonym if this is a new user
      await this.getOrCreatePseudonym(user.id);
      
      return user;
    } catch (error) {
      console.error('Error upserting user:', error);
      throw error;
    }
  }

  async createFromFirebase(firebaseUser: admin.auth.DecodedIdToken): Promise<User> {
    console.log('Creating user from Firebase:', firebaseUser);
    const version = process.env.CONSENT_VERSION || 'v1.0';
    
    const newUser = await this.prisma.user.create({
      data: {
        firebaseUid: firebaseUser.uid,
        email: firebaseUser.email || '',
        displayName: firebaseUser.name || null,
        emailVerified: firebaseUser.email_verified || false,
        photoURL: firebaseUser.picture || null,
        onboardingCompleted: false,
        creationTime: firebaseUser.auth_time ? new Date(firebaseUser.auth_time * 1000) : null,
        lastSignInTime: new Date(), // Current time as last sign in
        consents: {
          create: [
            {
              type: 'TERMS_OF_SERVICE',
              version,
              granted: true,
              timestamp: new Date(),
            },
          ],
        },
      },
    });

    // Create a pseudonym for the new user to ensure data consistency
    await this.getOrCreatePseudonym(newUser.id);
    
    return newUser;
  }

  async updateFromFirebase(id: string, firebaseUser: admin.auth.DecodedIdToken): Promise<User> {
    console.log('Updating user from Firebase:', firebaseUser);
    return this.prisma.user.update({
      where: { id },
      data: {
        displayName: firebaseUser.name || null,
        emailVerified: firebaseUser.email_verified || false,
        photoURL: firebaseUser.picture || null,
        lastSignInTime: new Date(), // Update last sign in time
      },
    });
  }

  async updateFirebaseUid(id: string, firebaseUid: string): Promise<User> {
    return this.prisma.user.update({
      where: { id },
      data: { firebaseUid },
    });
  }

  async delete(id: string): Promise<User> {
    return this.prisma.user.delete({
      where: { id },
    });
  }

  async findConsentByUserId(userId: string) {
    return this.prisma.userConsent.findMany({
      where: { userId },
    });
  }

  async updateProfileImageUrls(
    userId: string,
    profileImageUrl: string,
  ): Promise<User> {
    console.log('Updating profile image URL for user:', userId);
    return this.prisma.user.update({
      where: { id: userId },
      data: {
        photoURL: profileImageUrl || null, // Convert empty string to null
        updatedAt: new Date(),
      },
    });
  }

  async findUserCommunities(userId: string): Promise<{ id: string; name: string }[]> {
    const pseudonym = await this.findUserPseudonym(userId);

    if (!pseudonym) {
      console.log(`No pseudonym found for user: ${userId}`);
      return [];
    }

    const memberships = await this.prisma.communityMembership.findMany({
      where: { user_healthID: pseudonym.pseudonymId },
      include: {
        community: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return memberships.map((m) => m.community);
  }

  // Vault operations for pseudonym management
  async createUserPseudonym(userId: string): Promise<UserPseudonym> {
    console.log('Creating user pseudonym for user:', userId);
    return this.prisma.userPseudonym.create({
      data: {
        userId,
      },
    });
  }

  async findUserPseudonym(userId: string): Promise<UserPseudonym | null> {
    return this.prisma.userPseudonym.findUnique({
      where: { userId },
    });
  }

  async findByPseudonymId(pseudonymId: string): Promise<UserPseudonym | null> {
    return this.prisma.userPseudonym.findUnique({
      where: { pseudonymId },
    });
  }

  async getOrCreatePseudonym(userId: string): Promise<UserPseudonym> {
    // Try to find existing pseudonym
    let pseudonym = await this.findUserPseudonym(userId);
    
    // If not found, create a new one
    if (!pseudonym) {
      pseudonym = await this.createUserPseudonym(userId);
    }
    
    return pseudonym;
  }
}