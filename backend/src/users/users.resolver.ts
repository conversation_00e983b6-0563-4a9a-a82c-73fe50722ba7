import { Resolver, Query, Mutation, Args, ID, ResolveField, Parent } from '@nestjs/graphql';
import { UsersService } from './users.service';
import { User, UserConsent, CommunityInfo } from './user.entity';
import { CreateUserInput } from './dto/create-user.input';
import { Public } from '../auth/public.decorator';
import { CurrentUser } from '../auth/current-user.decorator';
import { GraphQLBoolean } from 'graphql';

@Resolver(() => User)
export class UsersResolver {
  constructor(private usersService: UsersService) {}

  @Query(() => [User])
  async users(): Promise<User[]> {
    return this.usersService.findAll();
  }

  @Query(() => User, { nullable: true })
  async user(@Args('id', { type: () => ID }) id: string): Promise<User | null> {
    return this.usersService.findOne(id);
  }

  @Query(() => User)
  async me(@CurrentUser() user: User): Promise<User> {
    // The CurrentUser decorator already gives us the authenticated user
    // Refetch from DB for freshest data
    const freshUser = await this.usersService.findOne(user.id);
    return freshUser || user;
  }

  @ResolveField('communities', () => [CommunityInfo], { nullable: true })
  async communities(@Parent() user: User) {
    const { id } = user;
    return this.usersService.findUserCommunities(id);
  }

  @ResolveField('consent', () => UserConsent, { nullable: true })
  async getConsent(@Parent() user: User) {
    const { id } = user;
    return this.usersService.findConsentByUserId(id);
  }

  @Public()
  @Mutation(() => User)
  async createUser(@Args('input') input: CreateUserInput): Promise<User> {
    console.log('Creating user via GraphQL mutation:', input);
    return this.usersService.upsertUser(input);
  }

  @Mutation(() => User)
  async deleteUser(@Args('id', { type: () => ID }) id: string): Promise<User> {
    return this.usersService.delete(id);
  }

  @Public()
  @Query(() => GraphQLBoolean, { name: 'checkUserExistsByEmail' })
  async checkUserExistsByEmail(
    @Args('email', { type: () => String }) email: string, // The 'email' variable from the GQL query is injected here
  ): Promise<boolean> {
    // It then calls the UsersService to handle the business logic
    return this.usersService.checkUserExistsByEmail(email);
  }
} 