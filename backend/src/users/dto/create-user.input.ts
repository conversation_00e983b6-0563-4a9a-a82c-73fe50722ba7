import { InputType, Field } from '@nestjs/graphql';
import { IsEmail, IsString, IsOptional, IsBoolean } from 'class-validator';

@InputType()
export class CreateUserInput {
  @Field(() => String)
  @IsString()
  firebaseUid: string;

  @Field(() => String)
  @IsEmail()
  email: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  displayName?: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  emailVerified?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  photoURL?: string;

  @Field(() => <PERSON>olean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  onboardingCompleted?: boolean;
} 