import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CompleteOnboardingInput } from './dto/complete-onboarding.input';
import { User, UserRole, Prisma } from '@prisma/client';
import { UsersService } from '../users/users.service';

// Define the shape of the parsed JSON objects
interface DiseaseUserType {
  role: 'patient' | 'caregiver';
  diagnosisStatus?: 'diagnosed' | 'not-diagnosed';
  diagnosisDate?: Date;
}

interface MedicationItem {
    id: string;
    label: string;
}

interface MedicationEntry {
    medication: MedicationItem;
    dosage?: string;
    frequency?: string;
    notes?: string;
    startDate?: Date;
    isCurrent: boolean;
}


@Injectable()
export class OnboardingService {
  private readonly logger = new Logger(OnboardingService.name);

  constructor(
    private prisma: PrismaService,
    private usersService: UsersService,
  ) {}

  private mapToUserRole(userType: DiseaseUserType): UserRole {
    if (userType.role === 'caregiver') {
      return UserRole.CAREGIVER;
    }
    if (userType.role === 'patient') { 
      if (userType.diagnosisStatus === 'diagnosed') {
        return UserRole.DIAGNOSED;
      }
      return UserRole.UNDIAGNOSED;
    }
    // Default fallback
    return UserRole.UNDIAGNOSED;
  }

  private async addUserToCommunities(
    tx: Prisma.TransactionClient,
    userHealthId: string,
    processedDiseases: Prisma.DiseaseGetPayload<{ include: { parent: true } }>[],
  ): Promise<void> {
    this.logger.log(
      `Starting community membership process for user ${userHealthId} with ${processedDiseases.length} processed diseases`,
    );

    // Step 1: For each selected disease, find its parent disease (or use itself if no parent)
    const communityTargetDiseaseIds = processedDiseases.map((dbDisease) => {
      if (dbDisease.parentId) {
        this.logger.log(
          `Disease ${dbDisease.name} (${dbDisease.id}) has parent disease ID: ${dbDisease.parentId}`,
        );
        return dbDisease.parentId;
      } else {
        this.logger.log(
          `Disease ${dbDisease.name} (${dbDisease.id}) has no parent, using itself for community lookup`,
        );
        return dbDisease.id;
      }
    });

    // Step 2: Get unique disease IDs to avoid duplicate community memberships
    const uniqueCommunityTargetDiseaseIds = [...new Set(communityTargetDiseaseIds)];
    this.logger.log(
      `After deduplication: ${uniqueCommunityTargetDiseaseIds.length} unique target diseases for communities`,
    );

    // Step 3: For each unique target disease, find its communities and add user
    let totalCommunitiesJoined = 0;

    for (const diseaseId of uniqueCommunityTargetDiseaseIds) {
      this.logger.log(`Finding communities for disease ID: ${diseaseId}`);

      const communities = await tx.community.findMany({
        where: {
          diseaseGroupId: diseaseId,
          isActive: true,
        },
      });

      this.logger.log(
        `Found ${communities.length} active communities for disease ID: ${diseaseId}`,
      );

      for (const community of communities) {
        this.logger.log(
          `Checking membership for community: ${community.name} (ID: ${community.id})`,
        );

        // Check if membership already exists to avoid duplicates
        const existingMembership = await tx.communityMembership.findUnique({
          where: {
            user_healthID_communityId: {
              user_healthID: userHealthId,
              communityId: community.id,
            },
          },
        });

        if (existingMembership) {
          this.logger.log(
            `User ${userHealthId} is already a member of community: ${community.name}`,
          );
        } else {
          await tx.communityMembership.create({
            data: {
              user_healthID: userHealthId,
              communityId: community.id,
              memberRole: 'MEMBER',
              joinedAt: new Date(),
            },
          });
          totalCommunitiesJoined++;
          this.logger.log(
            `Successfully added user ${userHealthId} to community: ${community.name}`,
          );
        }
      }
    }

    this.logger.log(
      `Community membership process completed. User ${userHealthId} joined ${totalCommunitiesJoined} new communities`,
    );
  }
  
  async completeOnboarding(
    userId: string,
    data: CompleteOnboardingInput,
  ): Promise<User> {
    this.logger.log(`Starting onboarding completion for user ${userId}`);

    const {
      consent,
      personalInfo,
      profilePicture,
      diseases,
      userTypes,
      medications,
    } = data;
      
    const pseudonym = await this.usersService.getOrCreatePseudonym(userId);
    if (!pseudonym) {
        throw new NotFoundException(`Pseudonym not found for user ${userId}`);
    }
    const userHealthId = pseudonym.pseudonymId;

    // Parse JSON string data from input
    const diseaseUserTypes: Record<string, DiseaseUserType> = JSON.parse(userTypes.diseaseUserTypes);
    const diseaseRelatedMedications: Record<string, MedicationEntry[]> = JSON.parse(medications.diseaseRelatedMedications);

    return this.prisma.$transaction(async (tx) => {
      // 1. Update User record with personal info and profile picture
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: {
          firstName: personalInfo.firstName,
          lastName: personalInfo.lastName,
          displayName: `${personalInfo.firstName} ${personalInfo.lastName}`,
          birthdate: personalInfo.birthdate,
          gender: personalInfo.gender,
          countryCode: personalInfo.countryCode,
          countryName: personalInfo.countryName,
          photoURL: profilePicture.imageUri,
          onboardingCompleted: true,
          updatedAt: new Date(),
        },
      });
      this.logger.log(`Updated user info for ${userId}`);

      // 2. Update UserConsent records
      const version = process.env.CONSENT_VERSION || 'v1.0';
      
      // Create consent records for each type
      // Terms of Service is handled at signup, not during onboarding
      const consentTypes = [
        { type: 'PRIVACY_POLICY' as const, granted: consent.dataPrivacy },
        { type: 'DATA_SHARING' as const, granted: consent.dataSharing },
        { type: 'MARKETING' as const, granted: consent.marketing },
      ];

      for (const { type, granted } of consentTypes) {
        await tx.userConsent.create({
          data: {
            userId,
            type,
            version,
            granted,
            timestamp: new Date(),
          },
        });
      }
      this.logger.log(`Updated consent records for ${userId}`);
      
      const diseaseToProfileIdMap = new Map<string, string>();
      const processedDbDiseases: Prisma.DiseaseGetPayload<{
        include: { parent: true };
      }>[] = [];

      // 3. Process diseases and user disease roles
      this.logger.log(
        `Processing ${diseases.selectedDiseases.length} diseases for user ${userId}`,
      );
      for (const disease of diseases.selectedDiseases) {
        // Find the disease by its ICD code
        const dbDisease = await tx.disease.findFirst({
          where: { icdCode: disease.icdCode },
          include: { parent: true }, // Include parent for community logic
        });

        if (!dbDisease) {
          // If disease is not found, it's a critical error as per requirements
          this.logger.error(
            `Disease with ICD code ${disease.icdCode} not found in database.`,
          );
          throw new NotFoundException(
            `Disease with ICD code ${disease.icdCode} not found.`,
          );
        }
        this.logger.log(
          `Found disease: ${dbDisease.name} (ID: ${dbDisease.id}) for ICD code ${disease.icdCode}`,
        );
        processedDbDiseases.push(dbDisease);

        const userType = diseaseUserTypes[disease.id];
        if (userType) {
          const userDiseaseRole = this.mapToUserRole(userType);

          const userDiseaseProfile = await tx.userDiseaseProfile.upsert({
            where: {
              user_healthID_diseaseId: {
                user_healthID: userHealthId,
                diseaseId: dbDisease.id,
              },
            },
            update: {
              userRole: userDiseaseRole,
              ...(userType.diagnosisDate && {
                diagnosisDate: new Date(userType.diagnosisDate),
              }),
            },
            create: {
              user_healthID: userHealthId,
              diseaseId: dbDisease.id,
              userRole: userDiseaseRole,
              ...(userType.diagnosisDate && {
                diagnosisDate: new Date(userType.diagnosisDate),
              }),
            },
          });
          diseaseToProfileIdMap.set(disease.id, userDiseaseProfile.id);
          this.logger.log(
            `Upserted UserDiseaseProfile for disease ${dbDisease.name} for user ${userId}`,
          );
        }
      }
      this.logger.log(
        `Processed ${diseases.selectedDiseases.length} diseases for user ${userId}`,
      );

      // 3a. Add user to communities based on selected diseases
      await this.addUserToCommunities(tx, userHealthId, processedDbDiseases);

      // 4. Process disease-related medications
      for (const [diseaseId, medicationEntries] of Object.entries(
        diseaseRelatedMedications,
      )) {
          const userDiseaseProfileId = diseaseToProfileIdMap.get(diseaseId);

          if (userDiseaseProfileId) {
              for (const entry of medicationEntries) {
                  const dbMedication = await tx.medicationMaster.upsert({
                      where: { name: entry.medication.label },
                      update: {},
                      create: { name: entry.medication.label },
                  });

                  await tx.userMedication.create({
                      data: {
                          user_healthID: userHealthId,
                          medicationMasterId: dbMedication.id,
                          userDiseaseProfileId: userDiseaseProfileId, // Link to the specific user disease
                          dosage: entry.dosage,
                          frequency: entry.frequency,
                          notes: entry.notes,
                          startDate: entry.startDate,
                          isCurrent: entry.isCurrent,
                      },
                  });
              }
          }
      }
      this.logger.log(`Processed disease-related medications for user ${userId}`);
      
      // 5. Process unrelated medications
      for (const entry of medications.unrelatedMedications) {
        const dbMedication = await tx.medicationMaster.upsert({
            where: { name: entry.medication.label },
            update: {},
            create: { name: entry.medication.label },
        });

        await tx.userMedication.create({
            data: {
                user_healthID: userHealthId,
                medicationMasterId: dbMedication.id,
                // userDiseaseProfileId is null for unrelated medications
                dosage: entry.dosage,
                frequency: entry.frequency,
                notes: entry.notes,
                startDate: entry.startDate,
                isCurrent: entry.isCurrent,
            },
        });
      }
      this.logger.log(`Processed unrelated medications for user ${userId}`);

      this.logger.log(`Onboarding successfully completed for user ${userId}`);
      return updatedUser;
    });
  }

  async updateProfilePicture(userId: string, photoURL: string): Promise<User> {
    this.logger.log(`Updating profile picture for user ${userId}`);
    
    return this.prisma.user.update({
      where: { id: userId },
      data: {
        photoURL,
        updatedAt: new Date(),
      },
    });
  }
}
