import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { OnboardingService } from './onboarding.service';
import { CurrentUser } from '../auth/current-user.decorator';
import { User } from '../users/user.entity';
import { CompleteOnboardingInput } from './dto/complete-onboarding.input';
import { UpdateProfilePictureInput } from './dto/update-profile-picture.input';

@Resolver()
export class OnboardingResolver {
  constructor(private readonly onboardingService: OnboardingService) {}

  @Mutation(() => Boolean)
  async completeOnboarding(
    @CurrentUser() user: User,
    @Args('data') data: CompleteOnboardingInput,
  ): Promise<boolean> {
    await this.onboardingService.completeOnboarding(user.id, data);
    return true;
  }

  @Mutation(() => User)
  async updateProfilePicture(
    @CurrentUser() user: User,
    @Args('photoURL') photoURL: string,
  ): Promise<User> {
    return this.onboardingService.updateProfilePicture(user.id, photoURL);
  }
}
