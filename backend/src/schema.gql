# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Author {
  authorId: String!
  condition: String
  displayName: String!
  photoURL: String
  userType: String!
}

type Comment {
  _id: ID!
  author: Author!
  content: String!
  createdAt: DateTime!
  parentCommentId: ID
  reactionCounts: ReactionCounts!
  replyCount: Int!
  status: String!
  threadId: ID!
  updatedAt: DateTime!
}

type CommunityInfo {
  id: ID!
  name: String!
}

input CompleteOnboardingInput {
  consent: ConsentInput!
  diseases: DiseaseDataInput!
  medications: MedicationDataInput!
  personalInfo: PersonalInfoInput!
  profilePicture: ProfilePictureInput!
  userTypes: UserTypeDataInput!
}

input ConsentInput {
  dataPrivacy: Boolean!
  dataSharing: Boolean!
  marketing: Boolean!
}

input CreateUserInput {
  displayName: String
  email: String!
  emailVerified: Boolean
  firebaseUid: String!
  onboardingCompleted: Boolean
  photoURL: String
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

input DiseaseDataInput {
  selectedDiseases: [DiseaseSelectionInput!]!
}

input DiseaseSelectionInput {
  icdCode: String!
  id: String!
}

input MedicationDataInput {
  diseaseRelatedMedications: String!
  unrelatedMedications: [MedicationEntryInput!]!
}

input MedicationEntryInput {
  dosage: String
  frequency: String
  isCurrent: Boolean!
  medication: MedicationItemInput!
  notes: String
  startDate: DateTime
}

input MedicationItemInput {
  id: String!
  label: String!
}

type Mutation {
  addReaction(reaction: String!, threadId: ID!): Thread!
  completeOnboarding(data: CompleteOnboardingInput!): Boolean!
  createProfileImageUploadUrl(contentType: String!): UploadUrlResponse!
  createUser(input: CreateUserInput!): User!
  deleteUser(id: ID!): User!
  updateProfilePicture(photoURL: String!): User!
  updateUserProfileImage(profileImageUrl: String!): User!
}

input PersonalInfoInput {
  birthdate: DateTime!
  countryCode: String!
  countryName: String!
  firstName: String!
  gender: String!
  lastName: String!
}

input ProfilePictureInput {
  imageUri: String
}

type Query {
  checkUserExistsByEmail(email: String!): Boolean!
  comments(limit: Int = 20, offset: Int = 0, threadId: ID!): [Comment!]!
  getFirebaseUid: String!
  getMyPseudonym: UserPseudonym!
  getMyPseudonymId: String!
  me: User!
  replies(limit: Int = 10, offset: Int = 0, parentCommentId: ID!): [Comment!]!
  thread(id: ID!): Thread!
  threads(communityId: String, limit: Int = 10, offset: Int = 0): [Thread!]!
  user(id: ID!): User
  users: [User!]!
}

type ReactionCounts {
  funny: Int!
  insightful: Int!
  love: Int!
  poop: Int!
  withYou: Int!
}

type Thread {
  _id: ID!
  author: Author!
  commentCount: Int!
  communityId: String!
  content: String!
  createdAt: DateTime!
  labels: [String!]!
  myReaction: String
  reactionCounts: ReactionCounts!
  status: String!
  title: String!
  updatedAt: DateTime!
}

type UploadUrlResponse {
  publicUrl: String!
  signedUrl: String!
}

type User {
  communities: [CommunityInfo!]
  consent: UserConsent
  createdAt: DateTime!
  creationTime: DateTime
  displayName: String
  email: String!
  emailVerified: Boolean!
  firebaseUid: String!
  firstName: String
  id: ID!
  lastName: String
  lastSignInTime: DateTime
  onboardingCompleted: Boolean!
  phoneNumber: String
  photoURL: String
  updatedAt: DateTime!
}

type UserConsent {
  consentDataPrivacy: Boolean!
  consentDataPrivacyAt: DateTime
  consentMarketing: Boolean!
  consentMarketingAt: DateTime
  consentTermsOfService: Boolean!
  consentTermsOfServiceAt: DateTime
  id: ID!
}

type UserPseudonym {
  createdAt: DateTime!
  id: ID!
  pseudonymId: String!
  updatedAt: DateTime!
  userId: String!
}

input UserTypeDataInput {
  diseaseUserTypes: String!
}