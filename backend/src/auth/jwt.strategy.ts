import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-custom';
import * as admin from 'firebase-admin';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy as any, 'firebase') {
  constructor(
    private authService: AuthService,
    private usersService: UsersService,
  ) {
    super();
  }

  async validate(req: any) {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('No token provided');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    try {
      // Verify the Firebase ID token using Firebase Admin SDK
      const decodedToken = await this.authService.verifyFirebaseToken(token);

      console.log('decodedToken verified');
      
      // Fetch the user from the database using the Firebase UID
      let user = await this.usersService.findByFirebaseUid(decodedToken.uid);
      
      if (!user) {
        // User is authenticated with Firebase, but doesn't exist in our DB.
        // Create the user record just-in-time.
        console.log(`User with UID ${decodedToken.uid} not found. Creating new user.`);
        user = await this.usersService.createFromFirebase(decodedToken);
      }

      // Return the complete user object, which is now guaranteed to exist.
      return user;
    } catch (error) {
      console.error('jwt.strategy.ts: Firebase token verification failed:', error);
      throw new UnauthorizedException('Invalid Firebase token');
    }
  }
} 