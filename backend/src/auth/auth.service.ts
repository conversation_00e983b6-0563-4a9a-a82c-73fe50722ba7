import { Injectable, UnauthorizedException } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { User } from '../users/user.entity';
import { Logger } from '@nestjs/common';
import * as admin from 'firebase-admin';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(private usersService: UsersService) {}

  async validateFirebaseUser(decodedToken: admin.auth.DecodedIdToken): Promise<User> {
    try {
      // Try to find existing user by Firebase UID
      this.logger.log('Validating Firebase user');
      let user = await this.usersService.findByFirebaseUid(decodedToken.uid);
      
      if (!user && decodedToken.email) {
        // Try to find by email if UID lookup fails
        user = await this.usersService.findByEmail(decodedToken.email);
        
        if (user) {
          // Update existing user with Firebase UID
          user = await this.usersService.updateFirebaseUid(user.id, decodedToken.uid);
          this.logger.log(`Updated existing user with Firebase UID: ${decodedToken.email}`);
        }
      }

      // If user doesn't exist, create a new one
      if (!user) {
        user = await this.usersService.createFromFirebase(decodedToken);
        this.logger.log(`Created new user from Firebase: ${decodedToken.email}`);
      } else {
        // Update user with latest Firebase data
        user = await this.usersService.updateFromFirebase(user.id, decodedToken);
        this.logger.log(`Updated user data from Firebase: ${decodedToken.email}`);
      }

      return user;
    } catch (error) {
      this.logger.error(`Error validating Firebase user: ${error.message}`);
      throw new UnauthorizedException('User validation failed');
    }
  }

  /**
   * Get or create a user from Firebase UID
   * This method should be called when you actually need the database user
   */
  async getOrCreateUserFromFirebaseUid(firebaseUid: string): Promise<User> {
    try {
      // Try to find existing user by Firebase UID
      let user = await this.usersService.findByFirebaseUid(firebaseUid);
      
      if (!user) {
        // If user doesn't exist, we need to fetch from Firebase to get user data
        const firebaseUser = await admin.auth().getUser(firebaseUid);
        
        // Create user from Firebase data
        const tokenLikeData = {
          uid: firebaseUser.uid,
          email: firebaseUser.email || '',
          name: firebaseUser.displayName || null,
          picture: firebaseUser.photoURL || null,
          email_verified: firebaseUser.emailVerified,
          auth_time: Math.floor(Date.now() / 1000), // Current time as auth time
          // Required DecodedIdToken fields (not used by createFromFirebase but needed for type)
          aud: '',
          exp: 0,
          firebase: { identities: {}, sign_in_provider: 'custom' },
          iat: 0,
          iss: '',
          sub: firebaseUser.uid,
        } as admin.auth.DecodedIdToken;
        
        user = await this.usersService.createFromFirebase(tokenLikeData);
        
        this.logger.log(`Created new user from Firebase UID: ${firebaseUid}`);
      }

      return user;
    } catch (error) {
      this.logger.error(`Error getting/creating user from Firebase UID: ${error.message}`);
      throw new UnauthorizedException('User creation failed');
    }
  }

  async verifyFirebaseToken(token: string): Promise<admin.auth.DecodedIdToken> {
    try {
      const decodedToken = await admin.auth().verifyIdToken(token);
      return decodedToken;
    } catch (error) {
      this.logger.error(`auth.service.ts: Firebase token verification failed: ${error.message}`);
      throw new UnauthorizedException('Invalid token');
    }
  }
} 