import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

export const CurrentUser = createParamDecorator(
  (data: unknown, context: ExecutionContext) => {
    const ctx = GqlExecutionContext.create(context);
    const request = ctx.getContext().req;
    
    // Return the Firebase user data from the JWT strategy
    return request.user;
  },
);

export const CurrentFirebaseUser = createParamDecorator(
  (data: unknown, context: ExecutionContext) => {
    const ctx = GqlExecutionContext.create(context);
    const request = ctx.getContext().req;
    
    // Return just the Firebase token data
    return request.user?.firebaseToken;
  },
);

/**
 * Decorator that returns the Firebase UID directly
 * Useful when you only need the UID and not the full user object
 */
export const CurrentFirebaseUid = createParamDecorator(
  (data: unknown, context: ExecutionContext) => {
    const ctx = GqlExecutionContext.create(context);
    const request = ctx.getContext().req;
    
    return request.user?.firebaseUid;
  },
); 