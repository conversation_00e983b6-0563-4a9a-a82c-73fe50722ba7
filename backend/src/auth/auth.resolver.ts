import { Resolver, Query } from '@nestjs/graphql';
import { AuthService } from './auth.service';
import { User, UserPseudonym } from '../users/user.entity';
import { UsersService } from '../users/users.service';
import { CurrentUser, CurrentFirebaseUid } from './current-user.decorator';
import { Logger } from '@nestjs/common';

@Resolver()
export class AuthResolver {
  constructor(
    private authService: AuthService,
    private usersService: UsersService,
  ) {}

  @Query(() => User)
  async me(@CurrentFirebaseUid() firebaseUid: string): Promise<User> {
    // Get or create the user in the database using the Firebase UID
    Logger.log('Got me request from user', firebaseUid);
    return this.authService.getOrCreateUserFromFirebaseUid(firebaseUid);
  }

  @Query(() => String)
  async getFirebaseUid(@CurrentFirebaseUid() firebaseUid: string): Promise<string> {
    // Return the Firebase UID directly
    return firebaseUid;
  }

  @Query(() => UserPseudonym)
  async getMyPseudonym(@CurrentFirebaseUid() firebaseUid: string): Promise<UserPseudonym> {
    // Get or create the user first, then get their pseudonym
    const user = await this.authService.getOrCreateUserFromFirebaseUid(firebaseUid);
    return this.usersService.getOrCreatePseudonym(user.id);
  }

  @Query(() => String)
  async getMyPseudonymId(@CurrentFirebaseUid() firebaseUid: string): Promise<string> {
    // Get or create the user first, then get their pseudonym ID
    const user = await this.authService.getOrCreateUserFromFirebaseUid(firebaseUid);
    const pseudonym = await this.usersService.getOrCreatePseudonym(user.id);
    return pseudonym.pseudonymId;
  }
} 