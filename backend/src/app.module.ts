import { Module } from '@nestjs/common';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { ConfigModule } from '@nestjs/config';
import { PassportModule } from '@nestjs/passport';
import { join } from 'path';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './prisma/prisma.module';
import { FirebaseModule } from './firebase/firebase.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { OnboardingModule } from './onboarding/onboarding.module';
import { StorageModule } from './storage/storage.module';
import { MongoModule } from './mongo/mongo.module';
import { CommunityModule } from './community/community.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      autoSchemaFile:
        process.env.NODE_ENV === 'production'
          ? true
          : join(process.cwd(), 'src/schema.gql'),
      sortSchema: true,
      playground: process.env.NODE_ENV !== 'production',
      introspection: process.env.NODE_ENV !== 'production',
      context: ({ req, res }) => ({ req, res, user: req.user }),
      formatError: (error) => {
        if (process.env.NODE_ENV !== 'production') {
          console.error('GraphQL Error:', error);
        }
        return {
          message: error.message,
          ...(process.env.NODE_ENV !== 'production' && {
            locations: error.locations,
            path: error.path,
            extensions: error.extensions,
          }),
        };
      },
    }),
    PassportModule,
    PrismaModule,
    FirebaseModule,
    AuthModule,
    UsersModule,
    OnboardingModule,
    StorageModule,
    MongoModule,
    CommunityModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
