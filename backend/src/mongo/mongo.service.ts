import { Injectable, OnModuleInit, OnModule<PERSON><PERSON>roy, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MongoClient, ServerApiVersion, Db } from 'mongodb';

@Injectable()
export class MongoService implements OnModuleInit, OnModuleDestroy {
  private client: MongoClient | null = null;
  private readonly logger = new Logger(MongoService.name);

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    await this.connect();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  private async connect() {
    try {
      const uri = this.configService.get<string>('MONGO_DATABASE_URL');
      if (!uri) {
        this.logger.warn('MONGO_DATABASE_URL is not set. Skipping MongoDB connection.');
        return;
      }

      this.client = new MongoClient(uri, {
        serverApi: {
          version: ServerApiVersion.v1,
          strict: true,
          deprecationErrors: true,
        },
        connectTimeoutMS: 5000,
        serverSelectionTimeoutMS: 5000,
      });

      await this.client.connect();

      await this.client.db('admin').command({ ping: 1 });
      this.logger.log('Successfully connected to MongoDB!');
    } catch (error) {
      this.logger.error('Failed to connect to MongoDB:', error.message);
      this.logger.warn('Application will start without a functioning MongoDB connection.');
      this.client = null;
    }
  }

  private async disconnect() {
    if (this.client) {
      await this.client.close();
      this.logger.log('Disconnected from MongoDB');
    }
  }

  getClient(): MongoClient | null {
    if (!this.client) {
      return null;
    }
    return this.client;
  }

  getDatabase(name: string): Db | null {
    const client = this.getClient();
    if (!client) {
      return null;
    }
    return client.db(name);
  }

  async ping(): Promise<boolean> {
    if (!this.client) {
      return false;
    }
    try {
      await this.client.db('admin').command({ ping: 1 });
      return true;
    } catch (error) {
      this.logger.error('MongoDB ping failed:', error);
      return false;
    }
  }
} 