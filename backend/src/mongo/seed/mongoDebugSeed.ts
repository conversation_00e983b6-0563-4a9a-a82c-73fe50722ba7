import { ConfigService } from '@nestjs/config';
import { MongoService } from '../mongo.service';
import { Collection, Db, ObjectId } from 'mongodb';

// --- Configuration ---
const NUM_THREADS = 20;
const NUM_USERS = 10;
const MAX_COMMENTS_PER_THREAD = 5;
const MAX_REPLIES_PER_COMMENT = 3;
const REACTIONS_TO_CREATE = 500;

// --- Static Data for Realism ---
const communityIds = ['cmc2126b800053vk6xevh4m51']; // Inflammatory Bowel Disease
const userTypes = ['Diagnosed', 'Undiagnosed', 'Caregiver'];
const conditions = ['IBD', "<PERSON><PERSON><PERSON>'s", 'UC', 'IBS'];
const photoURLs = [
    'https://lh3.googleusercontent.com/a/ACg8ocLxY89QEXDlfkYVBZIEPkJlxhcmcwk3am25-q609aC2-lg41O0P=s576-c-no',
    'https://ca.slack-edge.com/T089HL4V48H-U08AHUKT6KH-2ed9799011cb-512',
    'https://media.licdn.com/dms/image/v2/C4D03AQEwq9z0gJTSmA/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1624006004028?e=1755734400&v=beta&t=vMr4kUSBrfw9dRPN3QBYtGkqSUfQ0G7C_ea24zr4YgM'
];

const realNames = [
    'Emma Wilson',
    'James Chen', 
    'Sophia Patel',
    'Lucas Garcia',
    'Olivia Brown',
    'Ethan Kim',
    'Ava Martinez',
    'Noah Taylor',
    'Isabella Wong',
    'William Smith'
];

// Create a pool of users with real names and random conditions
const users = realNames.map(name => {
    const nameParts = name.toLowerCase().split(' ');
    return {
        authorId: `user_${nameParts[0]}_${nameParts[1]}`,
        displayName: name,
        userType: getRandomElement(userTypes),
        condition: getRandomElement(conditions),
        photoURL: getRandomElement(photoURLs),
    };
});

const threadTitles = [
    "Just diagnosed, where do I start?",
    "Has anyone tried the new X-1 supplement?",
    "Feeling overwhelmed this week, need some encouragement.",
    "A small victory I wanted to share!",
    "Tips for managing medication side effects?",
    "How do you explain your condition to friends and family?",
    "Looking for recommendations for a good specialist.",
    "The mental toll of a chronic illness is real.",
    "What are your go-to comfort activities on a bad day?",
    "Celebrating a milestone today!",
];
const threadContents = [
    "The doctor gave me the news yesterday and I'm still processing. There's so much information and I don't know what to do first. Any advice for a newcomer would be greatly appreciated.",
    "I saw an ad for the X-1 supplement and it claims to help with joint pain. I'm skeptical but also desperate. Has anyone here actually tried it? What was your experience?",
    "It's just been one of those weeks where everything feels like a struggle. The fatigue is hitting hard and my mood is low. Just wanted to vent to people who get it.",
    "After months of trying, I was finally able to walk around the block without stopping! It might seem small to others, but for me, it's a huge win. Feeling proud today.",
    "I started a new medication last week and the side effects are really getting to me. Nausea and headaches are the worst. Does anyone have tips for managing these?",
];
const commentBodies = [
    "Thanks for sharing, this is really helpful!",
    "I completely agree with this.",
    "I had a very similar experience.",
    "Sending you strength and support!",
    "That's a great question, I'd also like to know the answer.",
    "I'm so sorry you're going through that.",
    "Congratulations! That's amazing news.",
    "Have you considered getting a second opinion?",
    "This is exactly what I needed to hear today.",
    "Well said.",
];
const reactionTypes = ['love', 'withYou', 'funny', 'insightful', 'poop'];


// --- Helper Function ---
function getRandomElement<T>(arr: T[]): T {
    return arr[Math.floor(Math.random() * arr.length)];
}

async function seed() {
    console.log("Starting database population script for ChroniCare forum...");

    const configService = new ConfigService();
    const mongoService = new MongoService(configService);
    let mongoClient;

    try {
        await mongoService.onModuleInit();
        mongoClient = mongoService.getClient();
        const db: Db = mongoService.getDatabase('chronicare-forum-dev')!;
        if (!db) {
            throw new Error('Database not found');
        }
        
        const Threads: Collection = db.collection('Threads');
        const Comments: Collection = db.collection('Comments');
        const Reactions: Collection = db.collection('Reactions');

        // Clean up previous data to ensure a fresh start
        console.log("Clearing existing collections...");
        await Threads.deleteMany({});
        await Comments.deleteMany({});
        await Reactions.deleteMany({});

        // --- Create Debug Thread ---
        console.log("Creating debug thread with comments...");
        const debugThread = {
            communityId: communityIds[0],
            author: getRandomElement(users),
            title: "This is a thread with comments and replies",
            content: "This is a debug thread to test the comment hierarchy. It should have comments and nested replies.",
            labels: ['debug', 'test'],
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
            commentCount: 0,
            reactionCounts: { love: 0, withYou: 0, funny: 0, insightful: 0, poop: 0 },
        };
        const debugThreadResult = await Threads.insertOne(debugThread);
        const debugThreadId = debugThreadResult.insertedId;

        // First comment - no replies
        const comment1 = {
            threadId: debugThreadId,
            author: getRandomElement(users),
            parentCommentId: null,
            content: "This is the first comment. It should not have any replies.",
            status: 'visible',
            createdAt: new Date(),
            updatedAt: new Date(),
            replyCount: 0,
            reactionCounts: { love: 0, withYou: 0, funny: 0, insightful: 0, poop: 0 },
        };
        await Comments.insertOne(comment1);
        await Threads.updateOne({ _id: debugThreadId }, { $inc: { commentCount: 1 } });

        // Second comment with one reply
        const comment2 = {
            threadId: debugThreadId,
            author: getRandomElement(users),
            parentCommentId: null,
            content: "This is the second comment. It should have exactly one reply.",
            status: 'visible',
            createdAt: new Date(),
            updatedAt: new Date(),
            replyCount: 0,
            reactionCounts: { love: 0, withYou: 0, funny: 0, insightful: 0, poop: 0 },
        };
        const comment2Result = await Comments.insertOne(comment2);
        await Threads.updateOne({ _id: debugThreadId }, { $inc: { commentCount: 1 } });

        // Reply to second comment
        const reply2 = {
            threadId: debugThreadId,
            author: getRandomElement(users),
            parentCommentId: comment2Result.insertedId,
            content: "This is a reply to the second comment.",
            status: 'visible',
            createdAt: new Date(),
            updatedAt: new Date(),
            replyCount: 0,
            reactionCounts: { love: 0, withYou: 0, funny: 0, insightful: 0, poop: 0 },
        };
        await Comments.insertOne(reply2);
        await Comments.updateOne({ _id: comment2Result.insertedId }, { $inc: { replyCount: 1 } });
        await Threads.updateOne({ _id: debugThreadId }, { $inc: { commentCount: 1 } });

        // Third comment with two replies, one having a nested reply
        const comment3 = {
            threadId: debugThreadId,
            author: getRandomElement(users),
            parentCommentId: null,
            content: "This is the third comment. It should have two replies, and one of those replies should have its own reply.",
            status: 'visible',
            createdAt: new Date(),
            updatedAt: new Date(),
            replyCount: 0,
            reactionCounts: { love: 0, withYou: 0, funny: 0, insightful: 0, poop: 0 },
        };
        const comment3Result = await Comments.insertOne(comment3);
        await Threads.updateOne({ _id: debugThreadId }, { $inc: { commentCount: 1 } });

        // First reply to third comment
        const reply3a = {
            threadId: debugThreadId,
            author: getRandomElement(users),
            parentCommentId: comment3Result.insertedId,
            content: "This is the first reply to the third comment.",
            status: 'visible',
            createdAt: new Date(),
            updatedAt: new Date(),
            replyCount: 0,
            reactionCounts: { love: 0, withYou: 0, funny: 0, insightful: 0, poop: 0 },
        };
        const reply3aResult = await Comments.insertOne(reply3a);
        await Comments.updateOne({ _id: comment3Result.insertedId }, { $inc: { replyCount: 1 } });
        await Threads.updateOne({ _id: debugThreadId }, { $inc: { commentCount: 1 } });

        // Second reply to third comment
        const reply3b = {
            threadId: debugThreadId,
            author: getRandomElement(users),
            parentCommentId: comment3Result.insertedId,
            content: "This is the second reply to the third comment. It should have a nested reply.",
            status: 'visible',
            createdAt: new Date(),
            updatedAt: new Date(),
            replyCount: 0,
            reactionCounts: { love: 0, withYou: 0, funny: 0, insightful: 0, poop: 0 },
        };
        const reply3bResult = await Comments.insertOne(reply3b);
        await Comments.updateOne({ _id: comment3Result.insertedId }, { $inc: { replyCount: 1 } });
        await Threads.updateOne({ _id: debugThreadId }, { $inc: { commentCount: 1 } });

        // Nested reply to second reply of third comment
        const nestedReply = {
            threadId: debugThreadId,
            author: getRandomElement(users),
            parentCommentId: reply3bResult.insertedId,
            content: "This is a nested reply to the second reply of the third comment.",
            status: 'visible',
            createdAt: new Date(),
            updatedAt: new Date(),
            replyCount: 0,
            reactionCounts: { love: 0, withYou: 0, funny: 0, insightful: 0, poop: 0 },
        };
        await Comments.insertOne(nestedReply);
        await Comments.updateOne({ _id: reply3bResult.insertedId }, { $inc: { replyCount: 1 } });
        await Threads.updateOne({ _id: debugThreadId }, { $inc: { commentCount: 1 } });

        console.log("Debug thread and comments created successfully!");

    } catch (e) {
        console.error("An error occurred during script execution:", e);
    } finally {
        if (mongoClient) {
            await mongoService.onModuleDestroy();
        }
    }
}

seed().catch(e => {
    console.error("An error occurred during script execution:", e);
    process.exit(1);
});
