import { ConfigService } from '@nestjs/config';
import { MongoService } from '../mongo.service';
import { Collection, Db, ObjectId } from 'mongodb';

// --- Configuration ---
const NUM_THREADS = 20;
const NUM_USERS = 10;
const MAX_COMMENTS_PER_THREAD = 5;
const MAX_REPLIES_PER_COMMENT = 3;
const REACTIONS_TO_CREATE = 500;


// --- Static Data for Realism ---
const communityIds = ['cmcanp9pj0005p4rxo20jhvq0']; // Inflammatory Bowel Disease
const userTypes = ['Diagnosed', 'Undiagnosed', 'Caregiver'];
const conditions = ['IBD', "<PERSON><PERSON><PERSON>'s", 'UC', 'IBS'];
const photoURLs = [
    'https://lh3.googleusercontent.com/a/ACg8ocLxY89QEXDlfkYVBZIEPkJlxhcmcwk3am25-q609aC2-lg41O0P=s576-c-no',
    'https://ca.slack-edge.com/T089HL4V48H-U08AHUKT6KH-2ed9799011cb-512',
    'https://media.licdn.com/dms/image/v2/C4D03AQEwq9z0gJTSmA/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1624006004028?e=1755734400&v=beta&t=vMr4kUSBrfw9dRPN3QBYtGkqSUfQ0G7C_ea24zr4YgM'
];

const realNames = [
    'Emma Wilson',
    'James Chen',
    'Sophia Patel',
    'Lucas Garcia',
    'Olivia Brown',
    'Ethan Kim',
    'Ava Martinez',
    'Noah Taylor',
    'Isabella Wong',
    'William Smith'
];

// Create a pool of users with real names and random conditions
const users = realNames.map(name => {
    const nameParts = name.toLowerCase().split(' ');
    return {
        authorId: `user_${nameParts[0]}_${nameParts[1]}`,
        displayName: name,
        userType: getRandomElement(userTypes),
        condition: getRandomElement(conditions),
        photoURL: getRandomElement(photoURLs),
    };
});

const threadTitles = [
    "Just diagnosed, where do I start?",
    "Has anyone tried the new X-1 supplement?",
    "Feeling overwhelmed this week, need some encouragement.",
    "A small victory I wanted to share!",
    "Tips for managing medication side effects?",
    "How do you explain your condition to friends and family?",
    "Looking for recommendations for a good specialist.",
    "The mental toll of a chronic illness is real.",
    "What are your go-to comfort activities on a bad day?",
    "Celebrating a milestone today!",
];
const threadContents = [
    "The doctor gave me the news yesterday and I'm still processing. There's so much information and I don't know what to do first. Any advice for a newcomer would be greatly appreciated.",
    "I saw an ad for the X-1 supplement and it claims to help with joint pain. I'm skeptical but also desperate. Has anyone here actually tried it? What was your experience?",
    "It's just been one of those weeks where everything feels like a struggle. The fatigue is hitting hard and my mood is low. Just wanted to vent to people who get it.",
    "After months of trying, I was finally able to walk around the block without stopping! It might seem small to others, but for me, it's a huge win. Feeling proud today.",
    "I started a new medication last week and the side effects are really getting to me. Nausea and headaches are the worst. Does anyone have tips for managing these?",
];
const commentBodies = [
    "Thanks for sharing, this is really helpful!",
    "I completely agree with this.",
    "I had a very similar experience.",
    "Sending you strength and support!",
    "That's a great question, I'd also like to know the answer.",
    "I'm so sorry you're going through that.",
    "Congratulations! That's amazing news.",
    "Have you considered getting a second opinion?",
    "This is exactly what I needed to hear today.",
    "Well said.",
];
const reactionTypes = ['love', 'withYou', 'funny', 'insightful', 'poop'];


// --- Helper Function ---
function getRandomElement<T>(arr: T[]): T {
    return arr[Math.floor(Math.random() * arr.length)];
}

async function seed() {
    console.log("Starting database population script for ChroniCare forum...");

    const configService = new ConfigService();
    const mongoService = new MongoService(configService);
    let mongoClient;

    try {
        await mongoService.onModuleInit();
        mongoClient = mongoService.getClient();
        const db: Db = mongoService.getDatabase('chronicare-forum-dev')!;
        
        const Threads: Collection = db.collection('Threads');
        const Comments: Collection = db.collection('Comments');
        const Reactions: Collection = db.collection('Reactions');

        // Clean up previous data to ensure a fresh start
        console.log("Clearing existing collections...");
        await Threads.deleteMany({});
        await Comments.deleteMany({});
        await Reactions.deleteMany({});

        // --- 1. Create Threads ---
        console.log(`Creating ${NUM_THREADS} threads...`);
        const threadIds: ObjectId[] = [];
        for (let i = 0; i < NUM_THREADS; i++) {
            const thread = {
                communityId: getRandomElement(communityIds),
                author: getRandomElement(users),
                title: getRandomElement(threadTitles),
                content: getRandomElement(threadContents),
                labels: Math.random() > 0.5 ? ['question', 'support'] : ['experience', 'medication'],
                status: 'active',
                createdAt: new Date(),
                updatedAt: new Date(),
                commentCount: 0,
                reactionCounts: { love: 0, withYou: 0, funny: 0, insightful: 0, poop: 0 },
            };
            const result = await Threads.insertOne(thread);
            threadIds.push(result.insertedId);
        }
        console.log("Threads created successfully.");


        // --- 2. Create Comments and Replies ---
        console.log("Creating comments and replies...");
        const allCommentIds: ObjectId[] = [];

        // A function to create nested replies
        const createReplies = async (threadId: ObjectId, parentCommentId: ObjectId, depth: number) => {
            if (depth > 2) return; // Max reply depth of 3 (0, 1, 2)

            const numReplies = Math.floor(Math.random() * MAX_REPLIES_PER_COMMENT);
            for (let i = 0; i < numReplies; i++) {
                const reply = {
                    threadId: threadId,
                    author: getRandomElement(users),
                    parentCommentId: parentCommentId,
                    content: getRandomElement(commentBodies),
                    status: 'visible',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    replyCount: 0,
                    reactionCounts: { love: 0, withYou: 0, funny: 0, insightful: 0, poop: 0 },
                };
                const result = await Comments.insertOne(reply);
                const newCommentId = result.insertedId;
                allCommentIds.push(newCommentId);

                // Increment counts on parent documents
                await Threads.updateOne({ _id: threadId }, { $inc: { commentCount: 1 } });
                await Comments.updateOne({ _id: parentCommentId }, { $inc: { replyCount: 1 } });
                
                // Recursively create more replies
                await createReplies(threadId, newCommentId, depth + 1);
            }
        };
        
        for (const threadId of threadIds) {
            const numComments = Math.floor(Math.random() * MAX_COMMENTS_PER_THREAD);
            for (let i = 0; i < numComments; i++) {
                const topLevelComment = {
                    threadId: threadId,
                    author: getRandomElement(users),
                    parentCommentId: null, // This indicates it's a top-level comment
                    content: getRandomElement(commentBodies),
                    status: 'visible',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    replyCount: 0,
                    reactionCounts: { love: 0, withYou: 0, funny: 0, insightful: 0, poop: 0 },
                };
                const result = await Comments.insertOne(topLevelComment);
                const newCommentId = result.insertedId;
                allCommentIds.push(newCommentId);

                // Increment comment count on the parent thread
                await Threads.updateOne({ _id: threadId }, { $inc: { commentCount: 1 } });
                
                // Create nested replies for this comment
                await createReplies(threadId, newCommentId, 0);
            }
        }
        console.log("Comments and replies created successfully.");


        // --- 3. Create Reactions ---
        console.log(`Creating ${REACTIONS_TO_CREATE} reactions...`);
        let reactionsAdded = 0;
        for (let i = 0; i < REACTIONS_TO_CREATE; i++) {
            const reactorId = getRandomElement(users).authorId;
            const reactionType = getRandomElement(reactionTypes);
            let documentId: ObjectId;
            let documentType: 'Thread' | 'Comment';
            let updateCollection: Collection;
            
            // Randomly decide to react to a thread or a comment
            if (Math.random() > 0.4 && allCommentIds.length > 0) {
                documentId = getRandomElement(allCommentIds);
                documentType = 'Comment';
                updateCollection = Comments;
            } else {
                documentId = getRandomElement(threadIds);
                documentType = 'Thread';
                updateCollection = Threads;
            }

            // Use upsert to avoid duplicate reactions from the same user on the same document
            const reactionResult = await Reactions.updateOne(
                { documentId: documentId, reactorId: reactorId },
                { $set: { documentType: documentType, reactionType: reactionType, createdAt: new Date() } },
                { upsert: true }
            );

            // Only increment the count if a new reaction was actually inserted
            if (reactionResult.upsertedCount > 0) {
                const incField = `reactionCounts.${reactionType}`;
                await updateCollection.updateOne({ _id: documentId }, { $inc: { [incField]: 1 } });
                reactionsAdded++;
            }
        }
        console.log(`${reactionsAdded} unique reactions created successfully.`);


        console.log("\n--- Population Summary ---");
        console.log(`Threads created: ${await Threads.countDocuments()}`);
        console.log(`Comments and replies created: ${await Comments.countDocuments()}`);
        console.log(`Reactions created: ${await Reactions.countDocuments()}`);
        console.log("Database population complete!");

    } catch (e) {
        console.error("An error occurred during script execution:", e);
    } finally {
        if (mongoClient) {
            await mongoService.onModuleDestroy();
        }
    }
}

seed().catch(e => {
    console.error("An error occurred during script execution:", e);
    process.exit(1);
});
