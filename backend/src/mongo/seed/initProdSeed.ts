import { ConfigService } from '@nestjs/config';
import { MongoService } from '../mongo.service';
import { Collection, Db, ObjectId } from 'mongodb';

// --- Static Data ---
const communityId = 'cmc2126b800053vk6xevh4m51'; // Inflammatory Bowel Disease

// The main user who will create the post
const mainUser = {
    authorId: `user_alex_dadi_reynisson`,
    displayName: '<PERSON> Dad<PERSON>',
    userType: 'Caregiver', // Example user type
    condition: 'IBD', // Example condition
    photoURL: 'https://lh3.googleusercontent.com/a/ACg8ocLxY89QEXDlfkYVBZIEPkJlxhcmcwk3am25-q609aC2-lg41O0P=s576-c-no',
};

// Some other users to create reactions
const reactionUsers = [
    {
        authorId: 'user_emma_wilson',
        displayName: 'Alex',
        userType: 'Diagnosed',
        condition: 'IBD',
        photoURL: 'https://lh3.googleusercontent.com/a/ACg8ocLxY89QEXDlfkYVBZIEPkJlxhcmcwk3am25-q609aC2-lg41O0P=s576-c-no',
    },
    {
        authorId: 'user_james_chen',
        displayName: 'Oliver Ravn',
        userType: 'Diagnosed',
        condition: "Crohn's",
        photoURL: 'https://media.licdn.com/dms/image/v2/C4D03AQEwq9z0gJTSmA/profile-displayphoto-shrink_800_800/profile-displayphoto-shrink_800_800/0/1624006004028?e=1755734400&v=beta&t=vMr4kUSBrfw9dRPN3QBYtGkqSUfQ0G7C_ea24zr4YgM'
    },
    {
        authorId: 'user_sophia_patel',
        displayName: 'Stefan Nagel',
        userType: 'Caregiver',
        condition: 'UC',
        photoURL: 'https://ca.slack-edge.com/T089HL4V48H-U08AHUKT6KH-2ed9799011cb-512',
    }
];

const reactionTypes = ['love', 'withYou', 'funny', 'insightful', 'poop'];

// --- Helper Function ---
function getRandomElement<T>(arr: T[]): T {
    return arr[Math.floor(Math.random() * arr.length)];
}

async function seed() {
    console.log("Starting initial production seed script for ChroniCare forum...");

    const configService = new ConfigService();
    const mongoService = new MongoService(configService);
    let mongoClient;

    try {
        await mongoService.onModuleInit();
        mongoClient = mongoService.getClient();
        const db: Db = mongoService.getDatabase('chronicare-forum-prod')!;
        
        const Threads: Collection = db.collection('Threads');
        const Comments: Collection = db.collection('Comments');
        const Reactions: Collection = db.collection('Reactions');

        // Clean up previous data to ensure a fresh start
        console.log("Clearing existing collections...");
        await Threads.deleteMany({});
        await Comments.deleteMany({});
        await Reactions.deleteMany({});

        // --- 1. Create The Main Thread ---
        console.log(`Creating the initial thread...`);
        
        const thread = {
            communityId: communityId,
            author: mainUser,
            title: "This is the community forum!",
            content: "Very welcome to our app! This is a space for all of us to connect, share experiences, and support each other on our health journeys.",
            labels: ['welcome', 'community'],
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
            commentCount: 0,
            reactionCounts: { love: 0, withYou: 0, funny: 0, insightful: 0, poop: 0 },
        };
        const result = await Threads.insertOne(thread);
        const threadId = result.insertedId;
        
        console.log("Initial thread created successfully.");


        // --- 2. Create Reactions ---
        console.log(`Creating reactions for the initial post...`);
        let reactionsAdded = 0;

        for (const user of reactionUsers) {
            const reactorId = user.authorId;
            const reactionType = getRandomElement(reactionTypes);
            
            const reactionResult = await Reactions.updateOne(
                { documentId: threadId, reactorId: reactorId },
                { $set: { documentType: 'Thread', reactionType: reactionType, createdAt: new Date() } },
                { upsert: true }
            );

            if (reactionResult.upsertedCount > 0) {
                const incField = `reactionCounts.${reactionType}`;
                await Threads.updateOne({ _id: threadId }, { $inc: { [incField]: 1 } });
                reactionsAdded++;
            }
        }
        
        console.log(`${reactionsAdded} unique reactions created successfully.`);


        console.log("\n--- Population Summary ---");
        console.log(`Threads created: ${await Threads.countDocuments()}`);
        console.log(`Comments and replies created: ${await Comments.countDocuments()}`);
        console.log(`Reactions created: ${await Reactions.countDocuments()}`);
        console.log("Database population complete!");

    } catch (e) {
        console.error("An error occurred during script execution:", e);
    } finally {
        if (mongoClient) {
            await mongoService.onModuleDestroy();
        }
    }
}

seed().catch(e => {
    console.error("An error occurred during script execution:", e);
    process.exit(1);
});
