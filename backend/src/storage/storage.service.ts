// src/storage/storage.service.ts
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Storage } from '@google-cloud/storage';
import * as path from 'path';

@Injectable()
export class StorageService {
  private readonly storage: Storage;
  private readonly bucketName: string;
  private readonly serviceAccountEmail: string;

  constructor(private readonly configService: ConfigService) {
    const nodeEnv = this.configService.get<string>('NODE_ENV');
    this.serviceAccountEmail = '<EMAIL>';
    
    // Use service account key for development, ADC for production
    if (nodeEnv === 'development') {
      const keyFilePath = path.join(process.cwd(), 'cloudKey.json');
      this.storage = new Storage({
        projectId: this.configService.get<string>('GOOGLE_CLOUD_PROJECT_ID'),
        keyFilename: keyFilePath,
      });
      console.log(`Using service account key file: ${keyFilePath}`);
    } else {
      // Production: Use Application Default Credentials
      this.storage = new Storage({
        projectId: this.configService.get<string>('GOOGLE_CLOUD_PROJECT_ID'),
      });
      console.log('Using Application Default Credentials');
      
      // Log the current service account being used
      this.logCurrentServiceAccount();
    }
    
    this.bucketName = this.configService.get<string>('GOOGLE_CLOUD_STORAGE_BUCKET') || '';
    console.log(`Storage bucket configured: ${this.bucketName}`);
    console.log(`Project ID configured: ${this.configService.get<string>('GOOGLE_CLOUD_PROJECT_ID')}`);
  }

  private async logCurrentServiceAccount() {
    try {
      const authClient = await this.storage.authClient.getClient();
      if (authClient && 'email' in authClient) {
        console.log(`Current service account: ${authClient.email}`);
      }
    } catch (error) {
      console.warn('Could not determine current service account:', error.message);
    }
  }

  /**
   * Debug method to check current authentication
   */
  async getCurrentServiceAccount(): Promise<string | null> {
    try {
      const authClient = await this.storage.authClient.getClient();
      if (authClient && 'email' in authClient) {
        return authClient.email || null;
      }
      return null;
    } catch (error) {
      console.error('Error getting current service account:', error);
      return null;
    }
  }

  /**
   * Generates a v4 signed URL for uploading a file.
   * @param objectPath The full path for the object in the GCS bucket.
   * @param contentType The MIME type of the file being uploaded.
   * @returns A promise that resolves to the signed URL string.
   */
  async generateUploadUrl(
    objectPath: string,
    contentType: string,
  ): Promise<string> {
    console.log(`Generating SIGNED URL for: ${objectPath}, contentType: ${contentType}`);
    
    // Log which service account we're using
    const currentAccount = await this.getCurrentServiceAccount();
    console.log(`🔍 Current service account for signed URL: ${currentAccount || 'UNKNOWN'}`);
    
    const options = {
      version: 'v4' as const,
      action: 'write' as const,
      expires: Date.now() + 15 * 60 * 1000, // 15 minutes 
      contentType,
      // Let Cloud Run use its own service account for signing
      // No need to specify signingEndpoint when running as the correct service account
    };

    try {
      const [url] = await this.storage
        .bucket(this.bucketName)
        .file(objectPath)
        .getSignedUrl(options);

      console.log('✅ Successfully generated signed URL:', url);
      return url;
    } catch (error) {
      console.error('❌ Error generating signed URL:', error);
      console.error('Error details:', {
        message: error.message,
        code: error.code,
        projectId: this.configService.get<string>('GOOGLE_CLOUD_PROJECT_ID'),
        bucketName: this.bucketName,
        serviceAccount: currentAccount,
        expectedServiceAccount: this.serviceAccountEmail
      });
      throw error;
    }
  }

  /**
   * Constructs the public URL for a given object path.
   * @param objectPath The full path for the object in the GCS bucket.
   * @returns The public URL string.
   */
  getPublicUrl(objectPath: string): string {
    return `https://storage.googleapis.com/${this.bucketName}/${objectPath}`;
  }
}