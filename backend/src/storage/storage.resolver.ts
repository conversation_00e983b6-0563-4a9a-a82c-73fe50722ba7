import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { CurrentUser } from '../auth/current-user.decorator';
import { User } from '../users/user.entity';
import { StorageService } from './storage.service';
import { UsersService } from '../users/users.service';
import { UploadUrlResponse } from './dto/upload.dto';

@Resolver()
export class StorageResolver {
  constructor(
    private readonly storageService: StorageService,
    private readonly usersService: UsersService,
  ) {}

  @Mutation(() => UploadUrlResponse)
  async createProfileImageUploadUrl(
    @CurrentUser() user: User,
    @Args('contentType') contentType: string,
  ): Promise<UploadUrlResponse> {
    // Create a unique file path for the user's profile image
    const timestamp = Date.now();
    // Use a generic extension, the contentType header is what matters
    const objectPath = `profile-images/${user.id}/${timestamp}.jpeg`;
    
    // Generate the signed upload URL
    const signedUrl = await this.storageService.generateUploadUrl(
      objectPath,
      contentType,
    );
    
    // Get the public URL that will be accessible after upload
    const publicUrl = this.storageService.getPublicUrl(objectPath);

    return {
      signedUrl,
      publicUrl,
    };
  }

  @Mutation(() => User)
  async updateUserProfileImage(
    @CurrentUser() user: User,
    @Args('profileImageUrl') profileImageUrl: string,
  ): Promise<User> {
    // Update the user's profile image URL in the database
    return this.usersService.updateProfileImageUrls(
      user.id,
      profileImageUrl,
    );
  }
}
