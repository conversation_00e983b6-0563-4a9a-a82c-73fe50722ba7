import { Test } from '@nestjs/testing';
import { UsersService } from '../users/users.service';
import { OnboardingService } from '../onboarding/onboarding.service';
import { PrismaService } from '../prisma/prisma.service';
import { CreateUserInput } from '../users/dto/create-user.input';
import { CompleteOnboardingInput } from '../onboarding/dto/complete-onboarding.input';
import * as crypto from 'crypto';

const CLEANUP = true;

describe('User Creation and Onboarding', () => {
  let usersService: UsersService;
  let onboardingService: OnboardingService;
  let prismaService: PrismaService;
  let createdUserId: string;
  let pseudonymId: string;

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        UsersService,
        OnboardingService,
        PrismaService,
      ],
    }).compile();

    usersService = moduleRef.get<UsersService>(UsersService);
    onboardingService = moduleRef.get<OnboardingService>(OnboardingService);
    prismaService = moduleRef.get<PrismaService>(PrismaService);
  });

  it('should create a user and complete onboarding', async () => {
    // Generate unique test data
    const uniqueId = crypto.randomBytes(4).toString('hex');
    const testEmail = `test-${uniqueId}@example.com`;
    
    // 1. Create user
    const createUserInput: CreateUserInput = {
      firebaseUid: `test-uid-${uniqueId}`,
      email: testEmail,
      displayName: `Test User ${uniqueId}`,
      emailVerified: true,
      onboardingCompleted: false,
    };
    
    const user = await usersService.upsertUser(createUserInput);
    expect(user).toBeDefined();
    expect(user.email).toBe(testEmail);
    createdUserId = user.id;
    
    // Get the pseudonym for later cleanup
    const pseudonym = await usersService.findUserPseudonym(user.id);
    expect(pseudonym).toBeDefined();
    pseudonymId = pseudonym?.pseudonymId || '';
    
    // 2. Complete onboarding
    const onboardingInput: CompleteOnboardingInput = {
      consent: {
        dataPrivacy: true,
        dataSharing: true,
        marketing: false,
      },
      personalInfo: {
        firstName: 'Test',
        lastName: 'User',
        birthdate: new Date('1990-01-01'),
        gender: 'male',
        countryCode: 'US',
        countryName: 'United States',
      },
      profilePicture: {
        imageUri: undefined,
      },
      diseases: {
        selectedDiseases: [
          {
            id: '1',
            icdCode: 'DD70', // Systemic lupus erythematosus
          }
        ],
      },
      userTypes: {
        diseaseUserTypes: JSON.stringify({
          '1': {
            role: 'patient',
            diagnosisStatus: 'diagnosed',
            diagnosisDate: new Date('2020-01-01'),
          },
        }),
      },
      medications: {
        diseaseRelatedMedications: JSON.stringify({
          '1': [
            {
              medication: {
                id: '1',
                label: 'Hydroxychloroquine',
              },
              dosage: '200mg',
              frequency: 'daily',
              notes: 'Test medication',
              startDate: new Date(),
              isCurrent: true,
            },
          ],
        }),
        unrelatedMedications: [],
      },
    };
    
    const updatedUser = await onboardingService.completeOnboarding(user.id, onboardingInput);
    expect(updatedUser).toBeDefined();
    expect(updatedUser.onboardingCompleted).toBe(true);
  });

  // Clean up after all tests
  afterAll(async () => {
    if (createdUserId && pseudonymId && CLEANUP) {
      console.log(`Cleaning up test data for user ${createdUserId} with pseudonym ${pseudonymId}`);
      
      // Use a transaction to ensure all related data is deleted properly
      await prismaService.$transaction(async (tx) => {
        // 1. Delete user medications (using pseudonymId)
        await tx.userMedication.deleteMany({
          where: { user_healthID: pseudonymId }
        });
        
        // 2. Delete user disease profiles (using pseudonymId)
        await tx.userDiseaseProfile.deleteMany({
          where: { user_healthID: pseudonymId }
        });
        
        // 3. Delete user pseudonym
        await tx.userPseudonym.delete({
          where: { userId: createdUserId }
        });
        
        // 4. Delete user consents
        await tx.userConsent.deleteMany({
          where: { userId: createdUserId }
        });
        
        // 5. Finally delete the user
        await tx.user.delete({
          where: { id: createdUserId }
        });
      });
      
      console.log('Test data cleanup completed successfully');
    }
  });
});
