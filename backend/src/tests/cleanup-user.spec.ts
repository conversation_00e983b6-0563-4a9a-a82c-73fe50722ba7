import { Test } from '@nestjs/testing';
import { PrismaService } from '../prisma/prisma.service';
import { UsersService } from '../users/users.service';

describe('Cleanup User Data', () => {
  let prismaService: PrismaService;
  let usersService: UsersService;
  const userEmail = ['<EMAIL>'];


  for (const email of userEmail) {
  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [PrismaService, UsersService],
    }).compile();

    prismaService = moduleRef.get<PrismaService>(PrismaService);
    usersService = moduleRef.get<UsersService>(UsersService);
  });

  it('should remove a user and all related data by email', async () => {
    if (!email) {
      throw new Error('Please provide an email address using the USER_EMAIL environment variable.');
    }

    console.log(`Attempting to clean up data for email: ${email}`);

    const user = await prismaService.user.findUnique({
      where: { email: email },
    });

    if (!user) {
      console.log(`No user found with email: ${email}. Nothing to clean up.`);
      return;
    }

    const createdUserId = user.id;
    const pseudonym = await usersService.findUserPseudonym(createdUserId);
    const pseudonymId = pseudonym?.pseudonymId;

    if (!pseudonymId) {
      console.warn(`Could not find pseudonym for user ${createdUserId}. Cleanup may be incomplete.`);
    }

    console.log(`Cleaning up test data for user ${createdUserId} with pseudonym ${pseudonymId}`);

    await prismaService.$transaction(async (tx) => {
      if (pseudonymId) {
        await tx.communityMembership.deleteMany({ where: { user_healthID: pseudonymId } });
        await tx.qualityOfLifeLog.deleteMany({ where: { user_healthID: pseudonymId } });
        await tx.userMedication.deleteMany({ where: { user_healthID: pseudonymId } });
        await tx.userDiseaseProfile.deleteMany({ where: { user_healthID: pseudonymId } });
        await tx.userPseudonym.deleteMany({ where: { userId: createdUserId } });
      }
      await tx.userConsent.deleteMany({ where: { userId: createdUserId } });
      await tx.user.delete({ where: { id: createdUserId } });
    });

  console.log('Test data cleanup completed successfully');
    });
  }
});