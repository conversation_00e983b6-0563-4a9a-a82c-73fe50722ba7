import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';

@Injectable()
export class FirebaseService implements OnModuleInit {
  constructor(private configService: ConfigService) {}

  onModuleInit() {
    // Check if Firebase is already initialized
    if (!admin.apps.length) {
      // Get Firebase project ID from environment variables
      const firebaseProjectId = this.configService.get('FIREBASE_PROJECT_ID', 'chronicare-454115');
      
      admin.initializeApp({
        // Explicitly set the project ID from environment variable
        projectId: firebaseProjectId,
        // Use Application Default Credentials for authentication
        // In production: uses the attached service account
        // In development: uses GOOGLE_APPLICATION_CREDENTIALS
      });
      
      console.log(`Firebase Admin SDK initialized successfully with project ID: ${firebaseProjectId}`);
    }
  }

  getAuth() {
    return admin.auth();
  } 
}