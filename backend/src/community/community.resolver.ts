import {
  Resolver,
  Query,
  Args,
  Int,
  ID,
  Mutation,
  Parent,
  ResolveField,
  Context,
} from '@nestjs/graphql';
import { CommunityService } from './community.service';
import { Thread, Comment } from './dto/community.models';
import { NotFoundException, UseGuards } from '@nestjs/common';
import { ObjectId } from 'mongodb';
import { CurrentUser } from '../auth/current-user.decorator';
import { User } from 'src/users/user.entity';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';

@Resolver(() => Thread)
export class CommunityResolver {
  constructor(private readonly communityService: CommunityService) {}

  @Query(() => [Thread], { name: 'threads' })
  @UseGuards(JwtAuthGuard)
  async getThreads(
    @CurrentUser() user: User,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 10 })
    limit: number,
    @Args('offset', { type: () => Int, nullable: true, defaultValue: 0 })
    offset: number,
    @Args('communityId', { type: () => String, nullable: true })
    communityId?: string,
  ) {
    // 1. Fetch the main documents (threads)
    const threads = await this.communityService.getThreads({
      limit,
      offset,
      communityId,
    });
    if (!user || threads.length === 0) {
      return threads;
    }

    // 2. Collect IDs for batch fetching
    const threadIds = threads.map((t) => t._id);

    // 3. Batch fetch user-specific data (reactions)
    const userReactions =
      await this.communityService.findUserReactionsForThreads(
        threadIds,
        user.id,
      );

    // 4. Create a map for quick lookups
    const reactionMap = new Map(
      userReactions.map((r) => [r.documentId.toHexString(), r.reactionType]),
    );

    // 5. Attach the user-specific data to each document
    threads.forEach((thread) => {
      thread.myReaction = reactionMap.get(thread._id.toHexString()) || null;
    });

    return threads;
  }

  @Query(() => Thread, { name: 'thread' })
  @UseGuards(JwtAuthGuard)
  async getThreadById(
    @CurrentUser() user: User,
    @Args('id', { type: () => ID }) id: string,
  ) {
    const thread = await this.communityService.getThreadById(id);
    if (!user || !thread) {
      return thread;
    }
    const reaction = await this.communityService.findUserReactionForThread(
      thread._id,
      user.id,
    );
    thread.myReaction = reaction ? reaction.reactionType : null;
    return thread;
  }

  @ResolveField('myReaction', () => String, { nullable: true })
  myReaction(@Parent() thread: Thread): string | null {
    // This will now be resolved by the parent queries (getThreads, getThreadById)
    // and attached directly. If it's not there, it's null.
    return thread.myReaction || null;
  }

  @Query(() => [Comment], { name: 'comments' })
  async getComments(
    @Args('threadId', { type: () => ID }) threadId: string,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 20 }) limit: number,
    @Args('offset', { type: () => Int, nullable: true, defaultValue: 0 }) offset: number,
  ) {
    return this.communityService.getCommentsForThread(threadId, { limit, offset });
  }

  @Query(() => [Comment], { name: 'replies' })
  async getReplies(
    @Args('parentCommentId', { type: () => ID }) parentCommentId: string,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 10 }) limit: number,
    @Args('offset', { type: () => Int, nullable: true, defaultValue: 0 }) offset: number,
  ) {
    return this.communityService.getRepliesForComment(parentCommentId, { limit, offset });
  }

  @Mutation(() => Thread, { name: 'addReaction' })
  @UseGuards(JwtAuthGuard)
  async addReaction(
    @CurrentUser() user: User,
    @Args('threadId', { type: () => ID }) threadId: string,
    @Args('reaction', { type: () => String }) reaction: string,
  ) {
    const thread = await this.communityService.addReactionToThread(
      threadId,
      user.id,
      reaction,
    );

    // After mutation, we need to manually add the user's reaction
    // to the returned object so the frontend gets the updated state.
    const userReaction = await this.communityService.findUserReactionForThread(
      thread._id,
      user.id,
    );
    thread.myReaction = userReaction ? userReaction.reactionType : null;
    return thread;
  }
}
