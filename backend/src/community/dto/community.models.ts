import { ObjectType, Field, ID, Int } from '@nestjs/graphql';
import { ObjectId } from 'mongodb';

@ObjectType()
export class ReactionCounts {
  @Field(() => Int)
  love: number;

  @Field(() => Int)
  withYou: number;

  @Field(() => Int)
  funny: number;

  @Field(() => Int)
  insightful: number;

  @Field(() => Int)
  poop: number;
}

@ObjectType()
export class Author {
  @Field()
  authorId: string;

  @Field()
  displayName: string;

  @Field({ nullable: true })
  condition?: string;

  @Field()
  userType: string;

  @Field({ nullable: true })
  photoURL?: string;
}

@ObjectType('Thread')
export class Thread {
  @Field(() => ID)
  _id: ObjectId;

  @Field()
  communityId: string;

  @Field(() => Author)
  author: Author;

  @Field()
  title: string;

  @Field()
  content: string;

  @Field(() => [String])
  labels: string[];

  @Field()
  status: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;

  @Field(() => Int)
  commentCount: number;

  @Field(() => ReactionCounts)
  reactionCounts: ReactionCounts;

  @Field(() => String, { nullable: true })
  myReaction?: string | null;
}

@ObjectType('Comment')
export class Comment {
    @Field(() => ID)
    _id: ObjectId;

    @Field(() => ID)
    threadId: ObjectId;

    @Field(() => Author)
    author: Author;

    @Field(() => ID, { nullable: true })
    parentCommentId?: ObjectId;

    @Field()
    content: string;

    @Field()
    status: string;

    @Field()
    createdAt: Date;

    @Field()
    updatedAt: Date;

    @Field(() => Int)
    replyCount: number;

    @Field(() => ReactionCounts)
    reactionCounts: ReactionCounts;
}

@ObjectType('Reaction')
export class Reaction {
  @Field(() => ID)
  _id: ObjectId;

  @Field(() => ID)
  documentId: ObjectId; // ID of the Thread or Comment

  @Field()
  documentType: 'Thread' | 'Comment';

  @Field()
  reactorId: string; // The ID of the user who reacted

  @Field()
  reactionType: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}