############################
# 1) builder stage:
############################
FROM node:22-alpine AS builder
WORKDIR /usr/src/app
RUN apk add --no-cache openssl ca-certificates \
 && update-ca-certificates

# for Prisma
RUN apk add --no-cache openssl

COPY package*.json ./
# install dev+prod so we can build TS
RUN npm ci --frozen-lockfile

# bring in source + Prisma schema
COPY . .

# generate Prisma client
RUN npx prisma generate

# compile TS -> dist/
RUN npm run build


############################
# 2) production stage:
############################
FROM node:22-alpine AS production
WORKDIR /usr/src/app
ENV NODE_ENV=production

# create non-root user
RUN addgroup -g 1001 -S nodejs \
 && adduser   -S nestjs -u 1001

# copy only what we need:
#  • package.json & lockfile (so node_modules layout matches)
#  • production dependencies
COPY package*.json ./
RUN apk add --no-cache openssl
RUN apk add --no-cache openssl ca-certificates \
 && update-ca-certificates
RUN npm ci --only=production --frozen-lockfile

# copy build output + Prisma client
COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/node_modules/.prisma ./node_modules/.prisma

# create src directory for GraphQL schema generation and set ownership
RUN mkdir -p /usr/src/app/src && chown -R nestjs:nodejs /usr/src/app

# switch user and expose port
USER nestjs
EXPOSE 3000

# entrypoint
CMD ["node", "dist/src/main.js"]