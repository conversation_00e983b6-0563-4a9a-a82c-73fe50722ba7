require('dotenv').config();
const { spawn } = require('child_process');

const nodeEnv = process.env.NODE_ENV || 'development';

// Set the database URL based on environment
let databaseUrl;
if (nodeEnv === 'production') {
  databaseUrl = process.env.PROD_DATABASE_URL;
} else {
  databaseUrl = process.env.DEV_DATABASE_URL;
}

console.log(`🌍 Environment: ${nodeEnv}`);
console.log(`🗄️ Using database: ${nodeEnv === 'production' ? 'Production' : 'Development'}`);

// If this script is run with arguments, execute them with the correct environment
if (process.argv.length > 2) {
  const command = process.argv[2];
  const args = process.argv.slice(3);
  
  const env = {
    ...process.env,
    DATABASE_URL: databaseUrl,
  };
  
  const child = spawn(command, args, {
    stdio: 'inherit',
    env: env,
  });
  
  child.on('close', (code) => {
    process.exit(code);
  });
} else {
  // Just set the environment variables if no command is provided
  process.env.DATABASE_URL = databaseUrl;
} 