// Theme definition for the ChroniCare app
// Defines colors, typography, and other shared styles
// with support for both light and dark modes

// Typography interface
import { Typography, Palette, Spacing, TextVariants } from './themeTypes';

  
  // Main theme interface
  export interface Theme {
    colors: Palette;
    typography: Typography;
    spacing: Spacing;
    textVariants: TextVariants;
  }
  
  // Shared typography settings
  const typography: Typography = {
    fontFamily: {
      inter: 'Inter_400Regular',
      interLight: 'Inter_300Light',
      interMedium: 'Inter_500Medium',
      interSemiBold: 'Inter_600SemiBold',
      interBold: 'Inter_700Bold',
      merryweather: 'Merriweather_400Regular',
      merryweatherLight: 'Merriweather_300Light',
      merryweatherBold: 'Merriweather_700Bold',
    },
    textSize: {
        xs2: 10,
        xs: 12,
        sm: 14,
        md: 16,
        lg: 18,
        xl: 20,
        xl2: 24,
        xl3: 30,
        xl4: 36,
        xl5: 48,
    },
    headingSize: {
        xs: 14,
        sm: 16,
        md: 18,
        lg: 20,
        xl: 24,
        xl2: 30,
        xl3: 36,
        xl4: 48,
        xl5: 60,
    },
    fontWeight: {
        hairline: '100',
        light: '300',
        regular: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
        extraBold: '800',
        black: '900',
    },
    letterSpacing: 0.1,
  };
  
  // Shared spacing and border radius settings
  const spacing: Spacing = {
    spacing: {
        s0: 0,
        s0_5: 2,
        s1: 4,
        s1_5: 6,
        s2: 8,
        s2_5: 10,
        s3: 12,
        s3_5: 14,
        s4: 16,
        s4_5: 18,
        s5: 20,
        s6: 24,
        s7: 28,
        s8: 32,
        s9: 36,
        s10: 40,
        s11: 44,
        s12: 48,
        s16: 64,
        s20: 80,
        s24: 96,
        s32: 128,
        s40: 160,
        s48: 192,
        s56: 224,
        s64: 256,
        s72: 288,
        s80: 320,
        s96: 384,
    },
    borderWidth: {
        s0: 0,
        s1: 1,
        s2: 2,
        s4: 4,
        s8: 8,
    },
    borderRadius: {
        none: 0,
        xs: 2,
        sm: 4,
        md: 6,
        lg: 8,
        xl: 12,
        xl2: 16,
        xl3: 24,
        full: 9999
    }
  };
  
  
  // Light theme palette
  const lightPalette: Palette = {
    Primary: {
        primary0: '#ffffff',
        primary50: '#f1f3fe',
        primary100: '#e7ebfe',
        primary200: '#cdd6fe',
        primary300: '#b0beff',
        primary400: '#7b8eea',
        primary500: '#4361ee',
        primary600: '#3c58dd',
        primary700: '#334ecc',
        primary800: '#334599',
        primary900: '#2e396b',
        primary950: '#242942',
    },
    Secondary: {
        secondary0: '#ffffff',
        secondary50: '#fbe9ef',
        secondary100: '#fad1df',
        secondary200: '#fbb6ce',
        secondary300: '#ff81ad',
        secondary400: '#f20d83',
        secondary500: '#ff0059',
        secondary600: '#b8144e',
        secondary700: '#82173c',
        secondary800: '#4c192b',
        secondary900: '#270c16',
        secondary950: '#000000',
    },
    Tertiary: {
        tertiary0: '#ffffff',
        tertiary50: '#faf7f4',
        tertiary100: '#fbf1ea', 
        tertiary200: '#f9e3d2',
        tertiary300: '#f7c59f',
        tertiary400: '#f7926e',
        tertiary500: '#ff6b35',
        tertiary600: '#f24a0d',
        tertiary700: '#b84014',
        tertiary800: '#823417',
        tertiary900: '#522514',
        tertiary950: '#27140c',
    },
    Text: {
        text0: '#fefeff',
        text50: '#f5f5f5',
        text100: '#e5e5e5',
        text200: '#dbdbdc',
        text300: '#d4d4d4',
        text400: '#a3a3a3',
        text500: '#8c8c8c',
        text600: '#737373',
        text700: '#525252',
        text800: '#404040',
        text900: '#262627',
        text950: '#171717',
    },
    Background: {
        background0: '#ffffff',
        background50: '#f6f6f6',
        background100: '#f2f1f1',
        background200: '#dcdbdb',
        background300: '#d5d4d4',
        background400: '#a2a3a3',
        background500: '#8e8e8e',
        background600: '#747474',
        background700: '#535252',
        background800: '#414040',
        background900: '#272625',
        background950: '#121212',
        backroundMuted: '#f7f8f7',
    },

    Success: {
        success0: '#ffffff',
        success50: '#e4fff4',
        success100: '#caffe8', 
        success200: '#aff3c9',
        success300: '#8be9b3',
        success400: '#57ea95',
        success500: '#1bf175',
        success600: '#1ad568',
        success700: '#1eb85e',
        success800: '#1d9f70',
        success900: '#197151',
        success950: '#144332',
        background: '#e4fff4',
    },
    Indicator: {
        primary: '#373737',
        info: '#4361ee', 
        error: '#ff0059',
    },

  };

  // Dark theme palette
  const darkPalette: Palette = {
    Primary: {
        primary0: '#18191b',
        primary50: '#1e2231',
        primary100: '#262d41',
        primary200: '#2f3856',
        primary300: '#374472',
        primary400: '#455496',
        primary500: '#4361ee',
        primary600: '#6480f7',
        primary700: '#7c93fa',
        primary800: '#96aafc',
        primary900: '#b3c5fe',
        primary950: '#d0daff',
    },
    Secondary: {
        secondary0: '#18191b',
        secondary50: '#24181e',
        secondary100: '#312028',
        secondary200: '#422734',
        secondary300: '#573244',
        secondary400: '#7e4161',
        secondary500: '#ff0059',
        secondary600: '#ff337a',
        secondary700: '#ff5c92',
        secondary800: '#ff81ad',
        secondary900: '#ffadc6',
        secondary950: '#ffd5e1',
    },
    Tertiary: {
        tertiary0: '#18191b',
        tertiary50: '#231f1d',
        tertiary100: '#2d2621',
        tertiary200: '#3b2e26',
        tertiary300: '#4c3a2d',
        tertiary400: '#6d4a36',
        tertiary500: '#ff6b35',
        tertiary600: '#ff814e',
        tertiary700: '#ff9b6e',
        tertiary800: '#ffb695',
        tertiary900: '#ffd2bd',
        tertiary950: '#ffece4',
    },
    Text: {
        text0: '#171717',
        text50: '#262627', 
        text100: '#404040',
        text200: '#525252',
        text300: '#737373',
        text400: '#8c8c8c',
        text500: '#a3a3a3',
        text600: '#d4d4d4',
        text700: '#dbdbdc',
        text800: '#e5e5e5',
        text900: '#f5f5f5',
        text950: '#fefeff',
    },
    Background: {
        background0: '#121212',
        background50: '#272625',
        background100: '#414040',
        background200: '#535252',
        background300: '#747474',
        background400: '#8e8e8e',
        background500: '#a2a3a3',
        background600: '#d5d4d4',
        background700: '#e5e4e4',
        background800: '#f2f1f1',
        background900: '#f6f6f6',
        background950: '#ffffff',
        backroundMuted: '#333333',
    },

    Success: {
        success0: '#19231b',
        success50: '#1f3b2e',
        success100: '#2b5743',
        success200: '#388a60',
        success300: '#42ad7a',
        success400: '#58ca91',
        success500: '#1bf175',
        success600: '#4bcd95',
        success700: '#66d19f',
        success800: '#7cd9aa',
        success900: '#85dbab',
        success950: '#91e2b8',
        background: '#1f3b2e',
    },
    Indicator: {
        primary: '#f7f7f7',
        info: '#a1c7f5',
        error: '#ff81ad',
    },
  };

  const createTextVariants = (colors: Palette, typography: Typography): TextVariants => {
    const textVariants = {
      heading: (size: keyof Typography['headingSize'] = 'md', weight: keyof Typography['fontWeight'] = 'bold') => {
        let fontFamily = typography.fontFamily.merryweather; // Default to regular
        if (weight === 'light') {
          fontFamily = typography.fontFamily.merryweatherLight;
        } else if (weight === 'bold') {
          fontFamily = typography.fontFamily.merryweatherBold;
        }

        return {
          fontFamily,
          fontSize: typography.headingSize[size],
          color: colors.Text.text950,
          letterSpacing: 0,
        };
      },
      text: (size: keyof Typography['textSize'] = 'md', weight: keyof Typography['fontWeight'] = 'regular') => {
        let fontFamily = typography.fontFamily.inter; // Default to regular
        if (weight === 'light') {
          fontFamily = typography.fontFamily.interLight;
        } else if (weight === 'medium') {
          fontFamily = typography.fontFamily.interMedium;
        } else if (weight === 'semibold') {
          fontFamily = typography.fontFamily.interSemiBold;
        } else if (weight === 'bold') {
          fontFamily = typography.fontFamily.interBold;
        }
        
        return {
          fontFamily,
          fontSize: typography.textSize[size],
          color: colors.Text.text950,
          letterSpacing: typography.letterSpacing,
        };
      },
      body: {},
      button: {},
    };
    
    textVariants.body = textVariants.text('md', 'regular');
    textVariants.button = textVariants.text('md', 'medium');
    
    return textVariants as TextVariants;
  };
  
  
  // Light theme
  export const lightTheme: Theme = {
    colors: lightPalette,
    typography,
    spacing,
    textVariants: createTextVariants(lightPalette, typography),
  };
  
  // Dark theme
  export const darkTheme: Theme = {
    colors: darkPalette,
    typography,
    spacing,
    textVariants: createTextVariants(darkPalette, typography),
  }; 