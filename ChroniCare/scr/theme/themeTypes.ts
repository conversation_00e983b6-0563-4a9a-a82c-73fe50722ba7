import { TextStyle } from 'react-native';

// Typography interface
export interface Typography {
    fontFamily: {
        inter: string;
        interLight: string;
        interMedium: string;
        interSemiBold: string;
        interBold: string;
        merryweather: string;
        merryweatherLight: string;
        merryweatherBold: string;
    };
    textSize: {
        xs2: number;
        xs: number;
        sm: number;
        md: number;
        lg: number;
        xl: number;
        xl2: number;
        xl3: number;
        xl4: number;
        xl5: number;
    };

    headingSize: {
        xs: number;
        sm: number;
        md: number;
        lg: number;
        xl: number;
        xl2: number;
        xl3: number;
        xl4: number;
        xl5: number;
    };
    
    fontWeight: {
        hairline: string;
        light: string;
        regular: string;
        medium: string;
        semibold: string;
        bold: string;
        extraBold: string;
        black: string;
    };
    
    letterSpacing: number;
}

// Colors palette interface
export interface Palette {
    Primary: {
        primary0: string;
        primary50: string;
        primary100: string;
        primary200: string;
        primary300: string;
        primary400: string;
        primary500: string;
        primary600: string;
        primary700: string;
        primary800: string;
        primary900: string;
        primary950: string;
    };
    Secondary: {
        secondary0: string;
        secondary50: string;
        secondary100: string;
        secondary200: string;
        secondary300: string;
        secondary400: string;
        secondary500: string;
        secondary600: string;
        secondary700: string;
        secondary800: string;
        secondary900: string;
        secondary950: string;
    };
    Tertiary: {
        tertiary0: string;
        tertiary50: string;
        tertiary100: string;
        tertiary200: string;
        tertiary300: string;
        tertiary400: string;
        tertiary500: string;
        tertiary600: string;
        tertiary700: string;
        tertiary800: string;
        tertiary900: string;
        tertiary950: string;
    };
    Text: {
        text0: string;
        text50: string;
        text100: string;
        text200: string;
        text300: string;
        text400: string;
        text500: string;
        text600: string;
        text700: string;
        text800: string;
        text900: string;
        text950: string;
    };
    Background: {
        background0: string;
        background50: string;
        background100: string;
        background200: string;
        background300: string;
        background400: string;
        background500: string;
        background600: string;
        background700: string;
        background800: string;
        background900: string;
        background950: string;
        backroundMuted: string;
    };

    Success: {
        success0: string;
        success50: string;
        success100: string;
        success200: string;
        success300: string;
        success400: string;
        success500: string;
        success600: string;
        success700: string;
        success800: string;
        success900: string;
        success950: string;
        background: string;
    };

    Indicator: {
        primary: string;
        info: string;
        error: string;
    };
}

export interface Spacing {
    spacing: {
        s0: number;
        s0_5: number;
        s1: number;
        s1_5: number;
        s2: number;
        s2_5: number;
        s3: number;
        s3_5: number;
        s4: number;
        s4_5: number;
        s5: number;
        s6: number;
        s7: number;
        s8: number;
        s9: number;
        s10: number;
        s11: number;
        s12: number;
        s16: number;
        s20: number;
        s24: number;
        s32: number;
        s40: number;
        s48: number;
        s56: number;
        s64: number;
        s72: number;
        s80: number;
        s96: number;
    };
    borderWidth: {
        s0: number;
        s1: number;
        s2: number;
        s4: number;
        s8: number;
    };
    borderRadius: {
        none: number;
        xs: number;
        sm: number;
        md: number;
        lg: number;
        xl: number;
        xl2: number;
        xl3: number;
        full: number;
    };
}

// Text variants interface for predefined text styles
export interface TextVariants {
    heading: (size?: keyof Typography['headingSize'], weight?: keyof Typography['fontWeight']) => TextStyle;
    text: (size?: keyof Typography['textSize'], weight?: keyof Typography['fontWeight']) => TextStyle;
    body: TextStyle;
    button: TextStyle;
}