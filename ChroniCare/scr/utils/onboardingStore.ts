import { create } from 'zustand';
import { ComboBoxItem } from '../components/forms/ComboBox';

// Type definitions for the onboarding state
export interface ConsentData {
  dataPrivacy: boolean;
  dataSharing: boolean;
  marketing: boolean;
}

export interface PersonalInfo {
  firstName: string;
  lastName: string;
  birthdate: Date | null;
  gender: string;
  countryCode: string;
  countryName: string;
}

export interface ProfilePictureData {
  imageUri: string | null;
}

export interface DiseaseData {
  selectedDiseases: ComboBoxItem[];
}

// Updated user type structure to include diagnosis status
export interface DiseaseUserType {
  role: 'patient' | 'caregiver';
  // Only relevant if role is 'patient'
  diagnosisStatus?: 'diagnosed' | 'not-diagnosed';
  // Only relevant if role is 'patient' and diagnosisStatus is 'diagnosed'
  diagnosisDate?: Date;
}

export interface UserTypeData {
  // Maps disease ID to user type with diagnosis status
  diseaseUserTypes: Record<string | number, DiseaseUserType>;
}

// Medication item structure (extends ComboBoxItem for consistency)
export interface MedicationItem extends ComboBoxItem {
  // No additional properties needed - only name and id from ComboBoxItem
}

// Individual medication entry with details
export interface MedicationEntry {
  medication: MedicationItem;
}

// Updated medication data structure
export interface MedicationData {
  // Medications linked to specific diseases
  diseaseRelatedMedications: Record<string | number, MedicationEntry[]>; // diseaseId -> medications
  
  // Medications not related to any specific disease
  unrelatedMedications: MedicationEntry[];
  
  // Optional: Track if user has completed medication entry for each disease
  completedDiseases: Set<string | number>;
}

// Progress tracking
export interface OnboardingProgress {
  currentStep: number;
  completedSteps: {
    consent: boolean;
    personalInfo: boolean;
    profilePicture: boolean;
    diseases: boolean;
    userTypes: boolean;
    medications: boolean;
  };
}

// Main onboarding state interface
export interface OnboardingState {
  // Data sections
  consent: ConsentData;
  personalInfo: PersonalInfo;
  profilePicture: ProfilePictureData;
  diseases: DiseaseData;
  userTypes: UserTypeData;
  medications: MedicationData;
  progress: OnboardingProgress;

  // Actions for consent
  updateConsent: (consent: Partial<ConsentData>) => void;
  setConsentComplete: () => void;

  // Actions for personal info
  updatePersonalInfo: (info: Partial<PersonalInfo>) => void;
  setPersonalInfoComplete: () => void;

  // Actions for profile picture
  setProfilePicture: (imageUri: string | null) => void;
  setProfilePictureComplete: () => void;

  // Actions for diseases
  setSelectedDiseases: (diseases: ComboBoxItem[]) => void;
  setDiseasesComplete: () => void;

  // Actions for user types
  setUserTypeForDisease: (
    diseaseId: string | number, 
    role: 'patient' | 'caregiver', 
    diagnosisStatus?: 'diagnosed' | 'not-diagnosed',
    diagnosisDate?: Date
  ) => void;
  setUserTypesComplete: () => void;
  getUserTypeForDisease: (diseaseId: string | number) => DiseaseUserType | undefined;
  areAllUserTypesComplete: () => boolean;

  // Actions for medications
  updateMedications: (medications: Partial<MedicationData>) => void;
  setMedicationsComplete: () => void;

  // Enhanced medication actions
  addMedicationToDisease: (diseaseId: string | number, medicationEntry: MedicationEntry) => void;
  removeMedicationFromDisease: (diseaseId: string | number, medicationId: string | number) => void;
  updateMedicationForDisease: (diseaseId: string | number, medicationId: string | number, updates: Partial<MedicationEntry>) => void;
  
  addUnrelatedMedication: (medicationEntry: MedicationEntry) => void;
  removeUnrelatedMedication: (medicationId: string | number) => void;
  updateUnrelatedMedication: (medicationId: string | number, updates: Partial<MedicationEntry>) => void;
  
  setDiseasemedicationComplete: (diseaseId: string | number) => void;
  getMedicationsForDisease: (diseaseId: string | number) => MedicationEntry[];
  getAllMedicationEntries: () => MedicationEntry[];
  isMedicationDataComplete: () => boolean;

  // Progress actions
  setCurrentStep: (step: number) => void;
  getCompletionPercentage: () => number;
  getCurrentStepProgress: () => number;

  // Methods to handle backward navigation
  setConsentIncomplete: () => void;
  setPersonalInfoIncomplete: () => void;
  setProfilePictureIncomplete: () => void;
  setDiseasesIncomplete: () => void;
  setUserTypesIncomplete: () => void;
  setMedicationsIncomplete: () => void;

  // Validation methods
  isConsentValid: () => boolean;
  isPersonalInfoValid: () => boolean;
  isDiseasesValid: () => boolean;
  canProceedToMedications: () => boolean;

  // Utility actions
  resetOnboarding: () => void;
  getOnboardingData: () => {
    consent: ConsentData;
    personalInfo: PersonalInfo;
    profilePicture: ProfilePictureData;
    diseases: DiseaseData;
    userTypes: UserTypeData;
    medications: MedicationData;
  };
}

// Initial state values
const initialConsentData: ConsentData = {
  dataPrivacy: false,
  dataSharing: false,
  marketing: false,
};

const initialPersonalInfo: PersonalInfo = {
  firstName: '',
  lastName: '',
  birthdate: null,
  gender: '',
  countryCode: '',
  countryName: '',
};

const initialProfilePictureData: ProfilePictureData = {
  imageUri: null,
};

const initialDiseaseData: DiseaseData = {
  selectedDiseases: [],
};

const initialUserTypeData: UserTypeData = {
  diseaseUserTypes: {},
};

const initialMedicationData: MedicationData = {
  diseaseRelatedMedications: {},
  unrelatedMedications: [],
  completedDiseases: new Set(),
};

const initialProgress: OnboardingProgress = {
  currentStep: 0,
  completedSteps: {
    consent: false,
    personalInfo: false,
    profilePicture: false,
    diseases: false,
    userTypes: false,
    medications: false,
  },
};

// Create the Zustand store
export const useOnboardingStore = create<OnboardingState>((set, get) => ({
  // Initial state
  consent: initialConsentData,
  personalInfo: initialPersonalInfo,
  profilePicture: initialProfilePictureData,
  diseases: initialDiseaseData,
  userTypes: initialUserTypeData,
  medications: initialMedicationData,
  progress: initialProgress,

  // Consent actions
  updateConsent: (consent) =>
    set((state) => ({
      consent: { ...state.consent, ...consent },
    })),

  setConsentComplete: () =>
    set((state) => ({
      progress: {
        ...state.progress,
        completedSteps: { ...state.progress.completedSteps, consent: true },
      },
    })),

  // Personal info actions
  updatePersonalInfo: (info) =>
    set((state) => ({
      personalInfo: { ...state.personalInfo, ...info },
    })),

  setPersonalInfoComplete: () =>
    set((state) => ({
      progress: {
        ...state.progress,
        completedSteps: { ...state.progress.completedSteps, personalInfo: true },
      },
    })),

  // Profile picture actions
  setProfilePicture: (imageUri) =>
    set((state) => ({
      profilePicture: { ...state.profilePicture, imageUri },
    })),

  setProfilePictureComplete: () =>
    set((state) => ({
      progress: {
        ...state.progress,
        completedSteps: { ...state.progress.completedSteps, profilePicture: true },
      },
    })),

  // Disease actions
  setSelectedDiseases: (diseases) =>
    set((state) => {
      // When diseases change, reset user types for removed diseases
      const existingDiseaseIds = new Set(diseases.map(d => d.id));
      const newUserTypes = Object.fromEntries(
        Object.entries(state.userTypes.diseaseUserTypes).filter(([diseaseId]) =>
          existingDiseaseIds.has(diseaseId)
        )
      );

      // Also clean up medications for removed diseases
      const newDiseaseRelatedMedications = Object.fromEntries(
        Object.entries(state.medications.diseaseRelatedMedications).filter(([diseaseId]) =>
          existingDiseaseIds.has(diseaseId)
        )
      );

      // Clean up completed diseases set
      const newCompletedDiseases = new Set(
        Array.from(state.medications.completedDiseases).filter(diseaseId =>
          existingDiseaseIds.has(diseaseId)
        )
      );

      return {
        diseases: { selectedDiseases: diseases },
        userTypes: { diseaseUserTypes: newUserTypes },
        medications: {
          ...state.medications,
          diseaseRelatedMedications: newDiseaseRelatedMedications,
          completedDiseases: newCompletedDiseases,
        },
      };
    }),

  setDiseasesComplete: () =>
    set((state) => ({
      progress: {
        ...state.progress,
        completedSteps: { ...state.progress.completedSteps, diseases: true },
      },
    })),

  // User type actions
  setUserTypeForDisease: (diseaseId, role, diagnosisStatus, diagnosisDate) =>
    set((state) => {
      const userType: DiseaseUserType = { role };
      
      // Only set diagnosis status for patients
      if (role === 'patient' && diagnosisStatus) {
        userType.diagnosisStatus = diagnosisStatus;
      }

      // Only set diagnosis date for patients with diagnosed status
      if (role === 'patient' && diagnosisStatus === 'diagnosed' && diagnosisDate) {
        userType.diagnosisDate = diagnosisDate;
      }

      return {
        userTypes: {
          diseaseUserTypes: {
            ...state.userTypes.diseaseUserTypes,
            [diseaseId]: userType,
          },
        },
      };
    }),

  setUserTypesComplete: () =>
    set((state) => ({
      progress: {
        ...state.progress,
        completedSteps: { ...state.progress.completedSteps, userTypes: true },
      },
    })),

  getUserTypeForDisease: (diseaseId) => {
    const state = get();
    return state.userTypes.diseaseUserTypes[diseaseId];
  },

  areAllUserTypesComplete: () => {
    const state = get();
    const selectedDiseases = state.diseases.selectedDiseases;
    const userTypes = state.userTypes.diseaseUserTypes;
    
    return selectedDiseases.every(disease => {
      const userType = userTypes[disease.id];
      
      if (!userType || !userType.role) {
        return false;
      }
      
      // If the role is patient, we need diagnosis status
      if (userType.role === 'patient') {
        return userType.diagnosisStatus === 'diagnosed' || userType.diagnosisStatus === 'not-diagnosed';
      }
      
      // If the role is caregiver, we don't need diagnosis status
      return userType.role === 'caregiver';
    });
  },

  // Medication actions
  updateMedications: (medications) =>
    set((state) => ({
      medications: { ...state.medications, ...medications },
    })),

  setMedicationsComplete: () =>
    set((state) => ({
      progress: {
        ...state.progress,
        completedSteps: { ...state.progress.completedSteps, medications: true },
      },
    })),

  // Enhanced medication actions
  addMedicationToDisease: (diseaseId, medicationEntry) =>
    set((state) => ({
      medications: {
        ...state.medications,
        diseaseRelatedMedications: {
          ...state.medications.diseaseRelatedMedications,
          [diseaseId]: [...state.medications.diseaseRelatedMedications[diseaseId] || [], medicationEntry],
        },
      },
    })),

  removeMedicationFromDisease: (diseaseId, medicationId) =>
    set((state) => ({
      medications: {
        ...state.medications,
        diseaseRelatedMedications: {
          ...state.medications.diseaseRelatedMedications,
          [diseaseId]: state.medications.diseaseRelatedMedications[diseaseId].filter(m => m.medication.id !== medicationId),
        },
      },
    })),

  updateMedicationForDisease: (diseaseId, medicationId, updates) =>
    set((state) => ({
      medications: {
        ...state.medications,
        diseaseRelatedMedications: {
          ...state.medications.diseaseRelatedMedications,
          [diseaseId]: state.medications.diseaseRelatedMedications[diseaseId].map(m =>
            m.medication.id === medicationId ? { ...m, ...updates } : m
          ),
        },
      },
    })),
  
  addUnrelatedMedication: (medicationEntry) =>
    set((state) => ({
      medications: {
        ...state.medications,
        unrelatedMedications: [...state.medications.unrelatedMedications, medicationEntry],
      },
    })),

  removeUnrelatedMedication: (medicationId) =>
    set((state) => ({
      medications: {
        ...state.medications,
        unrelatedMedications: state.medications.unrelatedMedications.filter(m => m.medication.id !== medicationId),
      },
    })),

  updateUnrelatedMedication: (medicationId, updates) =>
    set((state) => ({
      medications: {
        ...state.medications,
        unrelatedMedications: state.medications.unrelatedMedications.map(m =>
          m.medication.id === medicationId ? { ...m, ...updates } : m
        ),
      },
    })),
  
  setDiseasemedicationComplete: (diseaseId) =>
    set((state) => ({
      medications: {
        ...state.medications,
        completedDiseases: new Set(state.medications.completedDiseases).add(diseaseId),
      },
    })),

  getMedicationsForDisease: (diseaseId) => {
    const state = get();
    return state.medications.diseaseRelatedMedications[diseaseId] || [];
  },

  getAllMedicationEntries: () => {
    const state = get();
    return [...Object.values(state.medications.diseaseRelatedMedications).flat(), ...state.medications.unrelatedMedications];
  },

  isMedicationDataComplete: () => {
    const state = get();
    return state.medications.completedDiseases.size === state.diseases.selectedDiseases.length;
  },

  // Progress actions
  setCurrentStep: (step) =>
    set((state) => ({
      progress: { ...state.progress, currentStep: step },
    })),

  getCompletionPercentage: () => {
    const state = get();
    // Calculate progress based on current step position rather than just completed steps
    // This ensures the progress bar goes backwards when navigating back
    const totalSteps = 7; // consent, name, info, profilePicture, disease, userType, medication
    const currentProgress = Math.min(Math.max(state.progress.currentStep, 0), totalSteps);
    return Math.round((currentProgress / totalSteps) * 100);
  },

  // Method to get step-based progress (0-6)
  getCurrentStepProgress: () => {
    const state = get();
    return state.progress.currentStep;
  },

  // Methods to handle backward navigation by marking steps as incomplete
  setConsentIncomplete: () =>
    set((state) => ({
      progress: {
        ...state.progress,
        completedSteps: { ...state.progress.completedSteps, consent: false },
      },
    })),

  setPersonalInfoIncomplete: () =>
    set((state) => ({
      progress: {
        ...state.progress,
        completedSteps: { ...state.progress.completedSteps, personalInfo: false },
      },
    })),

  setProfilePictureIncomplete: () =>
    set((state) => ({
      progress: {
        ...state.progress,
        completedSteps: { ...state.progress.completedSteps, profilePicture: false },
      },
    })),

  setDiseasesIncomplete: () =>
    set((state) => ({
      progress: {
        ...state.progress,
        completedSteps: { ...state.progress.completedSteps, diseases: false },
      },
    })),

  setUserTypesIncomplete: () =>
    set((state) => ({
      progress: {
        ...state.progress,
        completedSteps: { ...state.progress.completedSteps, userTypes: false },
      },
    })),

  setMedicationsIncomplete: () =>
    set((state) => ({
      progress: {
        ...state.progress,
        completedSteps: { ...state.progress.completedSteps, medications: false },
      },
    })),

  // Validation methods
  isConsentValid: () => {
    const state = get();
    return state.consent.dataPrivacy && state.consent.dataSharing;
  },

  isPersonalInfoValid: () => {
    const state = get();
    const { firstName, lastName, birthdate, gender, countryCode } = state.personalInfo;
    return (
      firstName.trim() !== '' &&
      lastName.trim() !== '' &&
      birthdate !== null &&
      gender !== '' &&
      countryCode !== ''
    );
  },

  isDiseasesValid: () => {
    const state = get();
    return state.diseases.selectedDiseases.length > 0;
  },

  canProceedToMedications: () => {
    const state = get();
    return (
      state.progress.completedSteps.consent &&
      state.progress.completedSteps.personalInfo &&
      state.progress.completedSteps.profilePicture &&
      state.progress.completedSteps.diseases &&
      get().areAllUserTypesComplete()
    );
  },

  // Utility actions
  resetOnboarding: () =>
    set(() => ({
      consent: initialConsentData,
      personalInfo: initialPersonalInfo,
      profilePicture: initialProfilePictureData,
      diseases: initialDiseaseData,
      userTypes: initialUserTypeData,
      medications: initialMedicationData,
      progress: initialProgress,
    })),

  getOnboardingData: () => {
    const state = get();
    return {
      consent: state.consent,
      personalInfo: state.personalInfo,
      profilePicture: state.profilePicture,
      diseases: state.diseases,
      userTypes: state.userTypes,
      medications: state.medications,
    };
  },
}));

// Helper hooks for specific sections
export const useConsentStore = () => {
  const consent = useOnboardingStore((state) => state.consent);
  const updateConsent = useOnboardingStore((state) => state.updateConsent);
  const setConsentComplete = useOnboardingStore((state) => state.setConsentComplete);
  const isConsentValid = useOnboardingStore((state) => state.isConsentValid);
  
  return { consent, updateConsent, setConsentComplete, isConsentValid };
};

export const usePersonalInfoStore = () => {
  const personalInfo = useOnboardingStore((state) => state.personalInfo);
  const updatePersonalInfo = useOnboardingStore((state) => state.updatePersonalInfo);
  const setPersonalInfoComplete = useOnboardingStore((state) => state.setPersonalInfoComplete);
  const isPersonalInfoValid = useOnboardingStore((state) => state.isPersonalInfoValid);
  
  return { personalInfo, updatePersonalInfo, setPersonalInfoComplete, isPersonalInfoValid };
};

export const useDiseasesStore = () => {
  const diseases = useOnboardingStore((state) => state.diseases);
  const setSelectedDiseases = useOnboardingStore((state) => state.setSelectedDiseases);
  const setDiseasesComplete = useOnboardingStore((state) => state.setDiseasesComplete);
  const isDiseasesValid = useOnboardingStore((state) => state.isDiseasesValid);
  
  return { diseases, setSelectedDiseases, setDiseasesComplete, isDiseasesValid };
};

export const useUserTypesStore = () => {
  const userTypes = useOnboardingStore((state) => state.userTypes);
  const setUserTypeForDisease = useOnboardingStore((state) => state.setUserTypeForDisease);
  const getUserTypeForDisease = useOnboardingStore((state) => state.getUserTypeForDisease);
  const areAllUserTypesComplete = useOnboardingStore((state) => state.areAllUserTypesComplete);
  const setUserTypesComplete = useOnboardingStore((state) => state.setUserTypesComplete);
  
  // Helper function to set role and diagnosis status in one call
  const setPatientWithDiagnosis = (diseaseId: string | number, diagnosisStatus: 'diagnosed' | 'not-diagnosed', diagnosisDate?: Date) => {
    setUserTypeForDisease(diseaseId, 'patient', diagnosisStatus, diagnosisDate);
  };

  // Helper function to set caregiver (no diagnosis status needed)
  const setCaregiver = (diseaseId: string | number) => {
    setUserTypeForDisease(diseaseId, 'caregiver');
  };

  // Helper function to get role for a disease
  const getRoleForDisease = (diseaseId: string | number): 'patient' | 'caregiver' | undefined => {
    const userType = getUserTypeForDisease(diseaseId);
    return userType?.role;
  };

  // Helper function to get diagnosis status for a disease (only relevant for patients)
  const getDiagnosisStatusForDisease = (diseaseId: string | number): 'diagnosed' | 'not-diagnosed' | undefined => {
    const userType = getUserTypeForDisease(diseaseId);
    return userType?.diagnosisStatus;
  };

  // Helper function to get diagnosis date for a disease
  const getDiagnosisDateForDisease = (diseaseId: string | number): Date | undefined => {
    const userType = getUserTypeForDisease(diseaseId);
    return userType?.diagnosisDate;
  };

  // Helper function to get diseases where user is a diagnosed patient
  const getDiagnosedDiseases = (): ComboBoxItem[] => {
    const state = useOnboardingStore.getState();
    return state.diseases.selectedDiseases.filter(disease => {
      const userType = getUserTypeForDisease(disease.id);
      return userType?.role === 'patient' && userType?.diagnosisStatus === 'diagnosed';
    });
  };

  // Helper function to check if user has any diagnosed conditions
  const hasAnyDiagnosedConditions = (): boolean => {
    return getDiagnosedDiseases().length > 0;
  };

  // Helper function to check if user needs medication input
  const needsMedicationInput = (): boolean => {
    return hasAnyDiagnosedConditions();
  };
  
  return { 
    userTypes, 
    setUserTypeForDisease, 
    getUserTypeForDisease, 
    areAllUserTypesComplete,
    setUserTypesComplete,
    // Helper functions
    setPatientWithDiagnosis,
    setCaregiver,
    getRoleForDisease,
    getDiagnosisStatusForDisease,
    getDiagnosisDateForDisease,
    getDiagnosedDiseases,
    hasAnyDiagnosedConditions,
    needsMedicationInput,
  };
};

export const useMedicationsStore = () => {
  const medications = useOnboardingStore((state) => state.medications);
  const updateMedications = useOnboardingStore((state) => state.updateMedications);
  const setMedicationsComplete = useOnboardingStore((state) => state.setMedicationsComplete);
  
  // Enhanced medication actions
  const addMedicationToDisease = useOnboardingStore((state) => state.addMedicationToDisease);
  const removeMedicationFromDisease = useOnboardingStore((state) => state.removeMedicationFromDisease);
  const updateMedicationForDisease = useOnboardingStore((state) => state.updateMedicationForDisease);
  
  const addUnrelatedMedication = useOnboardingStore((state) => state.addUnrelatedMedication);
  const removeUnrelatedMedication = useOnboardingStore((state) => state.removeUnrelatedMedication);
  const updateUnrelatedMedication = useOnboardingStore((state) => state.updateUnrelatedMedication);
  
  const setDiseasemedicationComplete = useOnboardingStore((state) => state.setDiseasemedicationComplete);
  const getMedicationsForDisease = useOnboardingStore((state) => state.getMedicationsForDisease);
  const getAllMedicationEntries = useOnboardingStore((state) => state.getAllMedicationEntries);
  const isMedicationDataComplete = useOnboardingStore((state) => state.isMedicationDataComplete);

  // Get the selected diseases for context
  const selectedDiseases = useOnboardingStore((state) => state.diseases.selectedDiseases);

  // Helper function to create a medication entry
  const createMedicationEntry = (medication: MedicationItem): MedicationEntry => ({
    medication,
  });

  // Helper function to check if a disease has any medications
  const diseaseHasMedications = (diseaseId: string | number): boolean => {
    return getMedicationsForDisease(diseaseId).length > 0;
  };

  // Helper function to get medication count per disease
  const getMedicationCountForDisease = (diseaseId: string | number): number => {
    return getMedicationsForDisease(diseaseId).length;
  };

  // Helper function to get total medication count
  const getTotalMedicationCount = (): number => {
    return getAllMedicationEntries().length;
  };

  return { 
    medications, 
    updateMedications, 
    setMedicationsComplete,
    // Enhanced medication management
    addMedicationToDisease,
    removeMedicationFromDisease,
    updateMedicationForDisease,
    addUnrelatedMedication,
    removeUnrelatedMedication,
    updateUnrelatedMedication,
    setDiseasemedicationComplete,
    getMedicationsForDisease,
    getAllMedicationEntries,
    isMedicationDataComplete,
    // Context and helpers
    selectedDiseases,
    createMedicationEntry,
    diseaseHasMedications,
    getMedicationCountForDisease,
    getTotalMedicationCount,
  };
};

export const useOnboardingProgress = () => {
  const progress = useOnboardingStore((state) => state.progress);
  const setCurrentStep = useOnboardingStore((state) => state.setCurrentStep);
  const getCompletionPercentage = useOnboardingStore((state) => state.getCompletionPercentage);
  const canProceedToMedications = useOnboardingStore((state) => state.canProceedToMedications);
  
  return { progress, setCurrentStep, getCompletionPercentage, canProceedToMedications };
};