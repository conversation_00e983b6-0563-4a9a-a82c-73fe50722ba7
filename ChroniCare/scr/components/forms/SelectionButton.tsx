import React, { useMemo } from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import { SymbolView } from 'expo-symbols';
import { useTheme } from '../../context/themeContext';

interface SelectionButtonProps {
  isSelected: boolean;
  onPress: () => void;
  label: string;
  multiSelect?: boolean; // true for checkbox, false for radio
  disabled?: boolean;
}

const SelectionButton: React.FC<SelectionButtonProps> = ({ 
  isSelected, 
  onPress, 
  label, 
  multiSelect = false,
  disabled = false 
}) => {
  const { theme } = useTheme();

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: theme.spacing.spacing.s4,
      paddingHorizontal: theme.spacing.spacing.s5,
      borderWidth: theme.spacing.borderWidth.s1,
      borderColor: isSelected ? theme.colors.Primary.primary500 : theme.colors.Background.background300,
      borderRadius: theme.spacing.borderRadius.xl3,
      backgroundColor: isSelected ? theme.colors.Primary.primary50 : theme.colors.Background.background0,
      opacity: disabled ? 0.6 : 1,
    },
    selectedContainer: {
      borderColor: theme.colors.Primary.primary500,
      backgroundColor: theme.colors.Primary.primary50,
    },
    icon: {
      marginRight: theme.spacing.spacing.s3,
    },
    label: {
      ...theme.textVariants.text('md', 'medium'),
      color: isSelected ? theme.colors.Primary.primary700 : theme.colors.Text.text950,
      flex: 1,
    },
  }), [theme, isSelected, disabled]);

  const getIconName = () => {
    if (multiSelect) {
      return isSelected ? 'checkmark.square.fill' : 'square';
    } else {
      return isSelected ? 'checkmark.circle.fill' : 'circle';
    }
  };

  const getIconColor = () => {
    if (isSelected) {
      return theme.colors.Primary.primary500;
    }
    return theme.colors.Text.text400;
  };

  return (
    <TouchableOpacity 
      onPress={onPress} 
      style={styles.container}
      disabled={disabled}
      activeOpacity={0.7}
    >
      <SymbolView
        name={getIconName()}
        size={24}
        type="hierarchical"
        tintColor={getIconColor()}
        style={styles.icon}
      />
      <Text style={styles.label}>{label}</Text>
    </TouchableOpacity>
  );
};

export default SelectionButton; 