import React, { useState, useMemo, useRef, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { BottomSheetModal, BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { useTheme } from '../../context/themeContext';

interface DatePickerProps {
  label: string;
  placeholder?: string;
  value?: Date;
  onDateChange: (date: Date | undefined) => void;
  required?: boolean;
  error?: string;
  maximumDate?: Date;
  minimumDate?: Date;
}

export const DatePicker: React.FC<DatePickerProps> = ({
  label,
  placeholder = 'Press to select',
  value,
  onDateChange,
  required = false,
  error,
  maximumDate,
  minimumDate,
}) => {
  const [showDatePicker, setShowDatePicker] = useState(false);
  const { theme } = useTheme();

  // Bottom sheet ref and snap points
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['40%'], []);

  // Backdrop render function
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  const styles = useMemo(() => StyleSheet.create({
    container: {
      width: '100%',
    },
    label: {
      ...theme.textVariants.text('sm', 'medium'),
      color: theme.colors.Text.text950,
      marginBottom: theme.spacing.spacing.s1_5,
    },
    required: {
      color: theme.colors.Secondary.secondary500,
    },
    input: {
      paddingVertical: theme.spacing.spacing.s2,
      paddingRight: theme.spacing.spacing.s4,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Text.text950,
      backgroundColor: 'transparent',
    },
    inputText: {
      ...theme.textVariants.text('sm', 'regular'),
      color: value ? theme.colors.Text.text950 : theme.colors.Text.text500,
    },
    errorText: {
      color: theme.colors.Secondary.secondary500,
      fontSize: theme.typography.textSize.xs,
      marginTop: theme.spacing.spacing.s1,
    },
    // Bottom Sheet Date Picker Styles
    datePickerContainer: {
      backgroundColor: theme.colors.Background.background0,
      paddingVertical: theme.spacing.spacing.s4,
      paddingHorizontal: theme.spacing.spacing.s4,
      flex: 1,
    },
    datePickerHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.spacing.s4,
      paddingHorizontal: theme.spacing.spacing.s1,
    },
    datePickerTitle: {
      ...theme.textVariants.text('lg', 'semibold'),
      color: theme.colors.Text.text950,
    },
    doneButton: {
      backgroundColor: theme.colors.Background.background800,
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingVertical: theme.spacing.spacing.s2,
      borderRadius: theme.spacing.borderRadius.xl,
    },
    doneButtonText: {
      ...theme.textVariants.text('sm', 'medium'),
      color: theme.colors.Text.text0,
    },
  }), [theme, value]);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    // Handle date selection
    if (selectedDate && event.type !== 'dismissed') {
      onDateChange(selectedDate);
    }
    
    // Only close the picker on Android (automatic behavior) or when explicitly dismissed
    if (Platform.OS === 'android' || event.type === 'dismissed') {
      setShowDatePicker(false);
    }
  };

  const showDatepicker = () => {
    if (Platform.OS === 'ios') {
      bottomSheetModalRef.current?.present();
    } else {
      setShowDatePicker(true);
    }
  };

  // Function to handle tapping outside the date picker on iOS
  const handleBackdropPress = useCallback(() => {
    if (Platform.OS === 'ios') {
      bottomSheetModalRef.current?.dismiss();
    } else {
      setShowDatePicker(false);
    }
  }, []);

  return (
    <View style={styles.container}>
      <Text style={styles.label}>
        {label}
        {required && <Text style={styles.required}>*</Text>}
      </Text>
      
      <TouchableOpacity onPress={showDatepicker}>
        <View style={styles.input}>
          <Text style={styles.inputText}>
            {value ? formatDate(value) : placeholder}
          </Text>
        </View>
      </TouchableOpacity>
      
      {error && <Text style={styles.errorText}>{error}</Text>}

      {showDatePicker && Platform.OS === 'android' && (
        <DateTimePicker
          testID="dateTimePicker"
          value={value || new Date()}
          mode="date"
          is24Hour={true}
          display="default"
          onChange={handleDateChange}
          maximumDate={maximumDate}
          minimumDate={minimumDate}
          textColor={theme.colors.Text.text950}
        />
      )}
      
      {Platform.OS === 'ios' && (
        <BottomSheetModal
          ref={bottomSheetModalRef}
          snapPoints={snapPoints}
          enablePanDownToClose={true}
          onDismiss={() => setShowDatePicker(false)}
          backgroundStyle={{ backgroundColor: theme.colors.Background.background0 }}
          backdropComponent={renderBackdrop}
          handleIndicatorStyle={{
            backgroundColor: theme.colors.Background.background900,
          }}
        >
          <BottomSheetView style={styles.datePickerContainer}>
            <View style={styles.datePickerHeader}>
              <Text style={styles.datePickerTitle}>Select Date</Text>
              <TouchableOpacity style={styles.doneButton} onPress={handleBackdropPress}>
                <Text style={styles.doneButtonText}>Done</Text>
              </TouchableOpacity>
            </View>
            <DateTimePicker
              testID="dateTimePicker"
              value={value || new Date()}
              mode="date"
              is24Hour={true}
              display="spinner"
              onChange={handleDateChange}
              maximumDate={maximumDate}
              minimumDate={minimumDate}
              textColor={theme.colors.Text.text950}
              style={{ backgroundColor: 'transparent' }}
            />
          </BottomSheetView>
        </BottomSheetModal>
      )}
    </View>
  );
}; 