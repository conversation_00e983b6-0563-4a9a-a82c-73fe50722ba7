import React, { useState, useCallback, useMemo, useRef } from 'react';
import { View, Text, TextInput, ScrollView, TouchableOpacity, StyleSheet, StyleProp, ViewStyle, TextStyle, TouchableWithoutFeedback } from 'react-native';
import { X, ChevronDown } from 'lucide-react-native'; // Added ChevronDown for the down arrow
import { useTheme } from '../../context/themeContext'; // Adjust path as needed
import Label from './Label'; // Import the new Label component

export interface ComboBoxItem {
  id: string | number;
  label: string;
  [key: string]: any; // Allow for other properties
}

interface ComboBoxProps<T extends ComboBoxItem> {
  data: T[];
  onSelectionChange: (selectedItems: T[]) => void;
  placeholder?: string;
  label?: string;
  labelColorScheme?: 'primary' | 'secondary' | 'neutral' | 'success' | 'muted';
  multiple?: boolean;
  required?: boolean;
  selectedItems?: T[]; // Allow external control of selected items
  autoFocus?: boolean; // Add autoFocus prop
  containerStyle?: StyleProp<ViewStyle>;
  inputStyle?: StyleProp<TextStyle>;
  dropdownStyle?: StyleProp<ViewStyle>;
  listItemStyle?: StyleProp<ViewStyle>;
  listItemTextStyle?: StyleProp<TextStyle>;
  selectedItemPillStyle?: StyleProp<ViewStyle>;
  selectedItemTextStyle?: StyleProp<TextStyle>;
  renderListItem?: (item: T, isSelected: boolean, onPress: () => void) => React.ReactElement | null;
  renderSelectedItem?: (item: T, onRemove: () => void) => React.ReactElement | null;
  // Potentially add:
  // onSearch?: (query: string) => Promise<T[]>; // For async data fetching
  // loading?: boolean; // To show a loading indicator
  // disabled?: boolean;
}

const ComboBox = <T extends ComboBoxItem>({
  data,
  onSelectionChange,
  placeholder = "Search...",
  label,
  labelColorScheme = 'neutral',
  multiple = true,
  required = false,
  selectedItems: externalSelectedItems,
  autoFocus = false,
  containerStyle,
  inputStyle,
  dropdownStyle,
  listItemStyle,
  listItemTextStyle,
  selectedItemPillStyle,
  selectedItemTextStyle,
  renderListItem,
  renderSelectedItem,
}: ComboBoxProps<T>) => {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [internalSelectedItems, setInternalSelectedItems] = useState<T[]>([]);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const textInputRef = useRef<TextInput>(null);

  // Use external selectedItems if provided, otherwise use internal state
  const selectedItems = externalSelectedItems ?? internalSelectedItems;

  const filteredData = useMemo(() => {
    if (!searchQuery) {
      return [];
    }
    return data.filter(item =>
      item.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [data, searchQuery]);

  const handleSelectItem = useCallback((item: T) => {
    let newSelectedItems: T[];
    if (multiple) {
      if (selectedItems.find(selected => selected.id === item.id)) {
        // If already selected, don't add again
        return;
      } else {
        newSelectedItems = [...selectedItems, item];
      }
    } else {
      newSelectedItems = [item];
    }
    
    // Update internal state only if not using external state
    if (!externalSelectedItems) {
      setInternalSelectedItems(newSelectedItems);
    }
    
    onSelectionChange(newSelectedItems);
    setSearchQuery('');
    // Don't hide dropdown immediately, let the user continue selecting if multiple
    if (!multiple) {
      setIsDropdownVisible(false);
    }
  }, [multiple, onSelectionChange, selectedItems, externalSelectedItems]);

  const handleRemoveItem = useCallback((itemToRemove: T) => {
    const newSelectedItems = selectedItems.filter(item => item.id !== itemToRemove.id);
    
    // Update internal state only if not using external state
    if (!externalSelectedItems) {
      setInternalSelectedItems(newSelectedItems);
    }
    
    onSelectionChange(newSelectedItems);
  }, [onSelectionChange, selectedItems, externalSelectedItems]);

  const handleFocus = useCallback(() => {
    setIsDropdownVisible(true);
    setIsFocused(true);
  }, []);

  const handleBlur = useCallback(() => {
    // Only hide dropdown if we're not in the middle of selecting an item
    setTimeout(() => {
      setIsDropdownVisible(false);
      setIsFocused(false);
    }, 150);
  }, []);

  const defaultRenderListItem = (item: T, isSelected: boolean, onPress: () => void) => (
    <TouchableOpacity
      style={[componentStyles.listItem, listItemStyle, isSelected && componentStyles.selectedListItem]}
      onPress={onPress}
      activeOpacity={0.7}
      delayPressIn={100} // Add delay to allow scrolling
    >
      <Text style={[componentStyles.listItemText, { color: theme.colors.Text.text800 }, listItemTextStyle]}>{item.label}</Text>
    </TouchableOpacity>
  );

  const defaultRenderSelectedItem = (item: T, onRemove: () => void) => (
    <Label
      text={item.label}
      onRemove={onRemove}
      containerStyle={selectedItemPillStyle}
      textStyle={selectedItemTextStyle}
      colorScheme={labelColorScheme}
      variant="solid"
    />
  );

  // Styles using theme, renamed styles to componentStyles to avoid conflict if Label is in same file
  const componentStyles = useMemo(() => StyleSheet.create({
    container: {
      position: 'relative',
    },
    label: {
      ...theme.textVariants.text('sm', 'medium'),
      color: theme.colors.Text.text900,
      marginBottom: theme.spacing.spacing.s1,
    },
    required: {
      color: '#ff4757',
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Background.background700,
      paddingVertical: theme.spacing.spacing.s0_5,
    },
    input: {
      flex: 1,
      ...theme.textVariants.text('sm', 'regular'),
      color: theme.colors.Text.text900,
      minHeight: 30,
    },
    downArrow: {
      marginLeft: theme.spacing.spacing.s2,
    },
    dropdown: {
      position: 'absolute',
      top: '100%',
      left: 0,
      right: 0,
      backgroundColor: theme.colors.Background.background0,
      borderWidth: theme.spacing.borderWidth.s1,
      borderColor: theme.colors.Background.background200,
      borderRadius: theme.spacing.borderRadius.md,
      maxHeight: 136,
      zIndex: 1000,
      marginTop: theme.spacing.spacing.s1,
    },
    listItem: {
      paddingVertical: theme.spacing.spacing.s3,
      paddingHorizontal: theme.spacing.spacing.s4,
      borderBottomWidth: theme.spacing.borderWidth.s1,
      borderBottomColor: theme.colors.Background.background100,
    },
    selectedListItem: {
      backgroundColor: theme.colors.Primary.primary50,
    },
    listItemText: {
      ...theme.textVariants.text('md', 'regular'),
    },
    selectedItemsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: theme.spacing.spacing.s2,
      marginBottom: theme.spacing.spacing.s2,
    },
  }), [theme, isFocused]);

  return (
    <View style={[componentStyles.container, containerStyle]}>
      {label && (
        <Text style={componentStyles.label}>
          {label}
          {required && <Text style={componentStyles.required}>*</Text>}
        </Text>
      )}

      {multiple && selectedItems.length > 0 && (
        <View style={componentStyles.selectedItemsContainer}>
          {selectedItems.map(item => (
            <React.Fragment key={item.id}>
              {renderSelectedItem
                ? renderSelectedItem(item, () => handleRemoveItem(item))
                : defaultRenderSelectedItem(item, () => handleRemoveItem(item))}
            </React.Fragment>
          ))}
        </View>
      )}

      <View style={componentStyles.inputContainer}>
        <TextInput
          ref={textInputRef}
          style={[componentStyles.input, inputStyle]}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.Text.text500}
          value={searchQuery}
          onChangeText={(text) => {
            setSearchQuery(text);
            setIsDropdownVisible(true);
          }}
          onFocus={handleFocus}
          onBlur={handleBlur}
          autoFocus={autoFocus}
        />
        <ChevronDown size={20} color={theme.colors.Text.text900} style={componentStyles.downArrow} />
      </View>

      {isDropdownVisible && filteredData.length > 0 && (
        <TouchableWithoutFeedback>
          <View style={[componentStyles.dropdown, dropdownStyle]}>
            <ScrollView
              nestedScrollEnabled
              keyboardShouldPersistTaps="always" // This is crucial for preventing keyboard dismissal
            >
              {filteredData.map((item) => {
                const isSelected = selectedItems.some(si => si.id === item.id);
                const onPressItem = () => handleSelectItem(item);
                return (
                  <React.Fragment key={String(item.id)}>
                    {renderListItem
                      ? renderListItem(item, isSelected, onPressItem)
                      : defaultRenderListItem(item, isSelected, onPressItem)}
                  </React.Fragment>
                );
              })}
            </ScrollView>
          </View>
        </TouchableWithoutFeedback>
      )}
    </View>
  );
};

export default ComboBox; 