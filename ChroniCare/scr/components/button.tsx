import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps,
  View,
  TouchableHighlight,
} from 'react-native';
import { useTheme } from '../context/themeContext';
import Ionicons from '@expo/vector-icons/build/Ionicons';

export interface ButtonProps extends Omit<TouchableOpacityProps, 'style'> {
  title: string;
  backgroundColor?: string;
  textColor?: string;
  variant?: 'neutral' | 'secondary' | 'outline' | 'ghost' | 'iconNeutral' | 'iconOutline';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  icon?: keyof typeof Ionicons.glyphMap | React.ReactElement;
  iconPosition?: 'left' | 'right';
}

export const Button: React.FC<ButtonProps> = ({
  title,
  backgroundColor,
  textColor,
  variant = 'neutral',
  size = 'medium',
  loading = false,
  disabled = false,
  style,
  textStyle,
  onPress,
  icon,
  iconPosition = 'left',
  ...touchableProps
}) => {
  const { theme } = useTheme();

  // Get variant-specific styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'neutral':
        return {
          backgroundColor: backgroundColor || theme.colors.Background.background900,
          borderWidth: 0,
          borderColor: 'transparent',
        };
      case 'secondary':
        return {
          backgroundColor: backgroundColor || theme.colors.Secondary.secondary500,
          borderWidth: 0,
          borderColor: 'transparent',
        };
      case 'outline':
        return {
          backgroundColor: backgroundColor || 'transparent',
          borderWidth: 1,
          borderColor: theme.colors.Background.background900,
        };
      case 'ghost':
        return {
          backgroundColor: backgroundColor || 'transparent',
          borderWidth: 0,
          borderColor: 'transparent',
        };
      case 'iconNeutral':
        return {
          backgroundColor: backgroundColor || theme.colors.Background.background900,
          borderWidth: 0,
          borderColor: 'transparent',
        };
      case 'iconOutline':
        return {
          backgroundColor: backgroundColor || 'transparent',
          borderWidth: 1,
          borderColor: theme.colors.Background.background900,
        };
      default:
        return {
          backgroundColor: backgroundColor || theme.colors.Background.background900,
          borderWidth: 0,
          borderColor: 'transparent',
        };
    }
  };

  // Get text color based on variant
  const getTextColor = () => {
    if (textColor) return textColor;
    
    switch (variant) {
      case 'neutral':
        return theme.colors.Text.text0;
      case 'secondary':
        return theme.colors.Text.text950;
      case 'outline':
        return theme.colors.Text.text950;
      case 'ghost':
        return theme.colors.Text.text950;
      case 'iconNeutral':
        return theme.colors.Text.text0;
      case 'iconOutline':
        return theme.colors.Text.text950;
      default:
        return theme.colors.Text.text0;
    }
  };

  // Get size-specific styles
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          paddingHorizontal: theme.spacing.spacing.s4,
          paddingVertical: theme.spacing.spacing.s2,
          minHeight: 36,
        };
      case 'medium':
        return {
          paddingHorizontal: theme.spacing.spacing.s5,
          paddingVertical: theme.spacing.spacing.s3,
          minHeight: 40,
        };
      case 'large':
        return {
          paddingHorizontal: theme.spacing.spacing.s6,
          paddingVertical: theme.spacing.spacing.s5,
          minHeight: 56,
        };
      default:
        return {
          paddingHorizontal: theme.spacing.spacing.s5,
          paddingVertical: theme.spacing.spacing.s4,
          minHeight: 40,
        };
    }
  };

  const variantStyles = getVariantStyles();
  const sizeStyles = getSizeStyles();
  const currentTextColor = getTextColor();

  const isDisabled = disabled || loading;
  const opacity = isDisabled ? 0.6 : 1;

  // Use TouchableHighlight for outline variant to get better press feedback
  if (variant === 'outline' || variant === 'iconOutline') {
    return (
      <TouchableHighlight
        style={[
          styles.button,
          {
            backgroundColor: variantStyles.backgroundColor,
            borderWidth: variantStyles.borderWidth,
            borderColor: variantStyles.borderColor,
            borderRadius: theme.spacing.borderRadius.xl2,
            opacity,
            ...sizeStyles,
          },
          style,
        ]}
        onPress={loading ? undefined : onPress}
        disabled={isDisabled}
        underlayColor={theme.colors.Background.background100}
        accessibilityRole="button"
        accessibilityLabel={title}
        accessibilityState={{ disabled: isDisabled }}
        {...touchableProps}
      >
        {loading ? (
          <ActivityIndicator
            size="small"
            color={currentTextColor}
            style={styles.loader}
          />
        ) : (
          <View style={styles.content}>
            {iconPosition === 'left' && icon && (
              typeof icon === 'string' ? (
                <Ionicons
                  name={icon}
                  size={size === 'small' ? 16 : size === 'large' ? 20 : 18}
                  color={currentTextColor}
                  style={styles.icon}
                />
              ) : (
                <View style={styles.icon}>
                  {icon}
                </View>
              )
            )}
            <Text
              style={[
                theme.textVariants.button,
                {
                  color: currentTextColor,
                  fontSize: size === 'small' ? 14 : size === 'large' ? 18 : 16,
                },
                textStyle,
              ]}
            >
              {title}
            </Text>
            {iconPosition === 'right' && icon && (
              typeof icon === 'string' ? (
                <Ionicons
                  name={icon}
                  size={size === 'small' ? 16 : size === 'large' ? 20 : 18}
                  color={currentTextColor}
                  style={styles.iconRight}
                />
              ) : (
                <View style={styles.iconRight}>
                  {icon}
                </View>
              )
            )}
          </View>
        )}
      </TouchableHighlight>
    );
  }

  return (
    <TouchableOpacity
      style={[
        styles.button,
        {
          backgroundColor: variantStyles.backgroundColor,
          borderWidth: variantStyles.borderWidth,
          borderColor: variantStyles.borderColor,
          borderRadius: theme.spacing.borderRadius.xl2,
          opacity,
          ...sizeStyles,
        },
        style,
      ]}
      onPress={loading ? undefined : onPress}
      disabled={isDisabled}
      activeOpacity={0.8}
      accessibilityRole="button"
      accessibilityLabel={title}
      accessibilityState={{ disabled: isDisabled }}
      {...touchableProps}
    >
      {loading ? (
        <ActivityIndicator
          size="small"
          color={currentTextColor}
          style={styles.loader}
        />
      ) : (
        <View style={styles.content}>
          {variant === 'iconNeutral' && iconPosition === 'left' && icon && (
            typeof icon === 'string' ? (
              <Ionicons
                name={icon}
                size={size === 'small' ? 16 : size === 'large' ? 20 : 18}
                color={currentTextColor}
                style={styles.icon}
              />
            ) : (
              <View style={styles.icon}>
                {icon}
              </View>
            )
          )}
          <Text
            style={[
              theme.textVariants.button,
              {
                color: currentTextColor,
                fontSize: size === 'small' ? 14 : size === 'large' ? 18 : 16,
              },
              textStyle,
            ]}
          >
            {title}
          </Text>
          {variant === 'iconNeutral' && iconPosition === 'right' && icon && (
            typeof icon === 'string' ? (
              <Ionicons
                name={icon}
                size={size === 'small' ? 16 : size === 'large' ? 20 : 18}
                color={currentTextColor}
                style={styles.iconRight}
              />
            ) : (
              <View style={styles.iconRight}>
                {icon}
              </View>
            )
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  loader: {
    marginRight: 0,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    marginRight: 8,
  },
  iconRight: {
    marginLeft: 8,
  },
});

export default Button;
