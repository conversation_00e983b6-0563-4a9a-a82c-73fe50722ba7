import { View, Text, StyleSheet, Image, Pressable } from 'react-native'
import React, { useMemo } from 'react'
import { useTheme } from '@/scr/context/themeContext'
import Reactions from './reactions';
import CommentCounter from './comment';
import TimeStamp from './TimeStamp';
import { useThreadOptimized } from '@/scr/hooks/useThread';
import { useRouter } from 'expo-router';

interface ThreadCardProps {
    threadId: string;
    isClickable?: boolean;
}

const ThreadCard = ({ threadId, isClickable = true }: ThreadCardProps) => {
    const { theme } = useTheme();
    const thread = useThreadOptimized(threadId);
    const router = useRouter();

    const handlePress = () => {
        if (isClickable) {
            router.push(`/community/${threadId}`);
        }
    };

    const styles = useMemo(() => StyleSheet.create({
        postContainer: {
          gap: theme.spacing.spacing.s2,
          },
          authorContainer: {
            flexDirection: 'row',
          },
          authorImage: {
            width: 32,
            height: 32,
            borderRadius: 16,
            marginRight: theme.spacing.spacing.s2,
          },
          authorInfo: {
            flex: 1,
          },
          authorMetaContainer: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
          },
          authorName: {
            ...theme.textVariants.text('md', 'regular'),
            color: theme.colors.Text.text900,
          },
          authorDetails: {
            ...theme.textVariants.text('xs', 'regular'),
            color: theme.colors.Text.text700,
          },
          authorDetailsDot: {
            marginHorizontal: theme.spacing.spacing.s1,
          },
          postTitle: {
            ...theme.textVariants.heading('sm', 'bold'),
            color: theme.colors.Text.text900,
          },
          postContent: {
            ...theme.textVariants.text('sm', 'regular'),
            color: theme.colors.Text.text900,
          },
          actionBar: {
            flexDirection: 'row',
            alignItems: 'center',
            gap: 8,
          },
    }), [theme]);

    if (!thread) {
        return null;
    }
  return (
    <Pressable onPress={handlePress} disabled={!isClickable}>
    <View style={styles.postContainer}>
        <View style={styles.authorContainer}>
            <Image source={{ uri: thread.author.photoURL }} style={styles.authorImage} />
            <View style={styles.authorInfo}>
                <View style={styles.authorMetaContainer}>
                    <Text style={styles.authorName}>{thread.author.displayName}</Text>
                    <TimeStamp createdAt={thread.createdAt} />
                </View>
                <View style={{ flexDirection: 'row' }}>
                    <Text style={styles.authorDetails}>{thread.author.userType}</Text>
                    {thread.author.condition && (
                        <>
                            <Text style={[styles.authorDetails, styles.authorDetailsDot]}>•</Text>
                            <Text style={styles.authorDetails}>{thread.author.condition}</Text>
                        </>
                    )}
                </View>
            </View>
        </View>
        <Text style={styles.postTitle}>{thread.title}</Text>
        <Text style={styles.postContent}>{thread.content}</Text>
        <View style={styles.actionBar}>
            <Reactions threadId={thread._id} />
            <CommentCounter count={thread.commentCount} />
        </View>
    </View>
    </Pressable>
  )
}

export default React.memo(ThreadCard)