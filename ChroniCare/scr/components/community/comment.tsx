import { View, Text, StyleSheet } from 'react-native';
import React, { useMemo } from 'react';
import { useTheme } from '@/scr/context/themeContext';
import { MessagesSquare } from 'lucide-react-native';

interface CommentCounterProps {
  count: number;
}

const CommentCounter = ({ count }: CommentCounterProps) => {
  const { theme } = useTheme();

  const styles = useMemo(() => StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: theme.colors.Background.background0,
        borderRadius: 20,
        borderWidth: 1,
        borderColor: theme.colors.Text.text100,
        paddingVertical: theme.spacing.spacing.s1,
        paddingHorizontal: theme.spacing.spacing.s1,
        alignSelf: 'flex-start',
    },
    iconWrapper: {
        width: 24,
        height: 24,
        borderRadius: 14,
        backgroundColor: theme.colors.Background.background100,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: theme.spacing.spacing.s2,
    },
    countText: {
        ...theme.textVariants.text('sm', 'medium'),
        color: theme.colors.Text.text900,
    }
  }), [theme]);


  return (
    <View style={styles.container}>
        <View style={styles.iconWrapper}>
            <MessagesSquare size={16} color={theme.colors.Text.text900} strokeWidth={2.5} />
        </View>
        <Text style={styles.countText}>{count}</Text>
    </View>
  );
};

export default CommentCounter;
