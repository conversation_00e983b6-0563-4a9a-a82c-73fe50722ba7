import { View, Text, StyleSheet, Image } from 'react-native';
import React, { useMemo } from 'react';
import { useTheme } from '@/scr/context/themeContext';
import TimeStamp from './TimeStamp';
import Reactions from './reactions';
import { Author, ReactionCount } from '@/scr/graphql/fragments';

export interface Comment {
    _id: string;
    threadId: string;
    author: Author;
    content: string;
    createdAt: string;
    parentCommentId?: string;
    replyCount: number;
    reactionCounts: ReactionCount;
}

interface CommentCardProps {
    comment: Comment;
    nestingLevel?: number;
}

const CommentCard = ({ comment, nestingLevel = 0 }: CommentCardProps) => {
    const { theme } = useTheme();

    const styles = useMemo(() => StyleSheet.create({
        commentRow: {
            flexDirection: 'row',
        },
        indentationContainer: {
            width: 32, 
            alignItems: 'center',
        },
        verticalLine: {
            width: 1,
            flex: 1,
            backgroundColor: theme.colors.Background.background300,
        },
        contentContainer: {
            flex: 1,
            flexDirection: 'row',
            gap: theme.spacing.spacing.s2,
        },
        leftColumn: {
            width: 32,
            alignItems: 'center',
        },
        authorImage: {
            width: 32,
            height: 32,
            borderRadius: 16,
        },
        rightColumn: {
            flex: 1,
            paddingBottom: theme.spacing.spacing.s2,
        },
        authorInfo: {
            flex: 1,
            paddingBottom: theme.spacing.spacing.s1,
        },
        authorMetaContainer: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
        },
        authorName: {
            ...theme.textVariants.text('md', 'regular'),
            color: theme.colors.Text.text900,
        },
        authorDetails: {
            ...theme.textVariants.text('xs', 'regular'),
            color: theme.colors.Text.text700,
        },
        authorDetailsDot: {
            marginHorizontal: theme.spacing.spacing.s1,
        },
        commentContent: {
            ...theme.textVariants.text('sm', 'regular'),
            color: theme.colors.Text.text900,
        },
        actionBar: {
            flexDirection: 'row',
            alignItems: 'center',
            gap: 8,
            paddingBottom: theme.spacing.spacing.s1,
            paddingTop: theme.spacing.spacing.s1,
        },
        replyText: {
            ...theme.textVariants.text('sm', 'semibold'),
            color: theme.colors.Text.text600,
        }
    }), [theme]);

    if (!comment) {
        return null;
    }

    return (
        <View style={styles.commentRow}>
            {Array.from({ length: nestingLevel }).map((_, index) => (
                <View key={`indent-${index}`} style={styles.indentationContainer}>
                    <View style={styles.verticalLine} />
                </View>
            ))}
            <View style={styles.contentContainer}>
                <View style={styles.leftColumn}>
                    <Image source={{ uri: comment.author.photoURL }} style={styles.authorImage} />
                    <View style={styles.verticalLine} />
                </View>
                <View style={styles.rightColumn}>
                    <View style={styles.authorInfo}>
                        <View style={styles.authorMetaContainer}>
                            <Text style={styles.authorName}>{comment.author.displayName}</Text>
                            <TimeStamp createdAt={comment.createdAt} />
                        </View>
                        <View style={{ flexDirection: 'row' }}>
                            <Text style={styles.authorDetails}>{comment.author.userType}</Text>
                            {comment.author.condition && (
                                <>
                                    <Text style={[styles.authorDetails, styles.authorDetailsDot]}>•</Text>
                                    <Text style={styles.authorDetails}>{comment.author.condition}</Text>
                                </>
                            )}
                        </View>
                    </View>
                    <Text style={styles.commentContent}>{comment.content}</Text>
                    <View style={styles.actionBar}>
                        <Reactions threadId={comment._id} />
                    </View>
                </View>
            </View>
        </View>
    );
};

export default CommentCard; 