import { Text, StyleSheet } from 'react-native';
import React, { useMemo } from 'react';
import { useTheme } from '@/scr/context/themeContext';

interface TimeStampProps {
  createdAt: string;
}

const TimeStamp = ({ createdAt }: TimeStampProps) => {
  const { theme } = useTheme();

  const formattedTime = useMemo(() => {
    const now = new Date();
    const postDate = new Date(createdAt);
    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInMinutes < 60) {
      return `${diffInMinutes}m`;
    }

    if (diffInHours < 24) {
      return `${diffInHours}h`;
    }

    if (diffInDays < 6) {
      return `${diffInDays}d`;
    }

    // Format as DD.MM.YY
    return postDate.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: '2-digit'
    }).replace(/\//g, '.');
  }, [createdAt]);

  const styles = StyleSheet.create({
    timestamp: {
      ...theme.textVariants.text('xs', 'regular'),
      color: theme.colors.Text.text500,
    },
  });

  return <Text style={styles.timestamp}>{formattedTime}</Text>;
};

export default TimeStamp; 