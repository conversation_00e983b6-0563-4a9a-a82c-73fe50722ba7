import { View, Text, StyleSheet, Pressable, Modal, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import React, { useMemo, useState, useCallback } from 'react';
import * as Haptics from 'expo-haptics';
import { useTheme } from '@/scr/context/themeContext';
import { ReactionCount } from '@/scr/graphql/fragments';
import { useThreadOptimized } from '@/scr/hooks/useThread';
import { useThreadReactions } from '@/scr/hooks/useThreadReactions';
import { useReactionDisplay } from '@/scr/hooks/useReactionDisplay';

type ReactionType = keyof ReactionCount;

interface ReactionsProps {
  threadId: string;
}

const Reactions = ({ threadId }: ReactionsProps) => {
  const { theme } = useTheme();
  const thread = useThreadOptimized(threadId);
  const {
    addReaction,
    loading: reactionLoading,
    errorMessage,
  } = useThreadReactions(threadId);
  const [modalVisible, setModalVisible] = useState(false);

  // Use the custom hook for reaction display logic
  const { totalReactions, displayedReactions, iconMapping } = useReactionDisplay(thread?.reactionCounts || null);

  // Show error alert when errorMessage changes
  React.useEffect(() => {
    if (errorMessage) {
      Alert.alert(
        'Reaction Error',
        errorMessage,
        [
          {
            text: 'OK',
            onPress: () => {
              // Error message will be cleared by the hook
            },
          },
        ]
      );
    }
  }, [errorMessage]);

  const myReaction = thread?.myReaction as ReactionType | null;

  const handleQuickReaction = useCallback(() => {
    if (reactionLoading) return;

    // Add haptic feedback for quick reaction
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Toggles 'love' reaction
    addReaction('love');
  }, [reactionLoading, addReaction]);

  const handleSelectReaction = useCallback((reaction: ReactionType) => {
    if (reactionLoading) return;

    // Add haptic feedback for reaction selection
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    addReaction(reaction);
    setModalVisible(false);
  }, [reactionLoading, addReaction]);

  const handleLongPress = useCallback(() => {
    if (reactionLoading) return;

    // Add haptic feedback for long press
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);

    setModalVisible(true);
  }, [reactionLoading]);

  const styles = useMemo(
    () =>
      StyleSheet.create({
        container: {
          flexDirection: 'row',
          alignItems: 'center',
          borderRadius: 20,
          borderWidth: myReaction ? 1.5 : 1,
          borderColor: myReaction
            ? iconMapping[myReaction].color
            : theme.colors.Text.text100,
          backgroundColor: myReaction
            ? iconMapping[myReaction].colorLight
            : 'transparent',
          paddingVertical: theme.spacing.spacing.s1,
          paddingHorizontal: theme.spacing.spacing.s1,
          alignSelf: 'flex-start',
          opacity: reactionLoading ? 0.7 : 1, // Visual feedback for loading
        },
        loadingContainer: {
          flexDirection: 'row',
          alignItems: 'center',
          gap: theme.spacing.spacing.s1,
        },
        iconsContainer: {
          flexDirection: 'row',
          alignItems: 'center',
        },
        iconWrapper: {
          width: 24,
          height: 24,
          borderRadius: 14,
          backgroundColor: theme.colors.Background.background100,
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1,
        },
        overlappingIcon: {
          marginLeft: -8,
        },
        totalText: {
          ...theme.textVariants.text('sm', 'medium'),
          color: theme.colors.Text.text900,
          marginLeft: theme.spacing.spacing.s2,
        },
        modalContainer: {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
        },
        modalContent: {
          flexDirection: 'row',
          backgroundColor: theme.colors.Background.background100,
          borderRadius: 30,
          padding: theme.spacing.spacing.s4,
          elevation: 5,
          gap: theme.spacing.spacing.s3,
        },
      }),
    [theme, reactionLoading, myReaction],
  );

  return (
    <>
      <Pressable
        style={styles.container}
        onPress={handleQuickReaction}
        onLongPress={handleLongPress}
        disabled={reactionLoading}
        accessibilityRole="button"
        accessibilityLabel={`React to this post. Current reactions: ${totalReactions}. ${myReaction ? `You reacted with ${myReaction}` : 'Tap to love, long press for more options'}`}
        accessibilityHint="Tap to add a love reaction, or long press to see all reaction options"
        accessibilityState={{ disabled: reactionLoading }}>
        {reactionLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={theme.colors.Text.text900} />
            <Text style={styles.totalText}>Adding reaction...</Text>
          </View>
        ) : (
          <>
            <View style={styles.iconsContainer}>
              {displayedReactions.map(({ key, icon: Icon }, index) => (
                <View
                  key={key}
                  style={[
                    styles.iconWrapper,
                    index > 0 && styles.overlappingIcon,
                    { zIndex: displayedReactions.length - index },
                  ]}>
                  <Icon
                    size={16}
                    color={
                      myReaction === key
                        ? iconMapping[key].color
                        : theme.colors.Text.text900
                    }
                    fill={
                      myReaction === key ? iconMapping[key].color : 'transparent'
                    }
                    strokeWidth={2.5}
                  />
                </View>
              ))}
            </View>
            <Text style={styles.totalText}>{totalReactions}</Text>
          </>
        )}
      </Pressable>
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
        accessibilityViewIsModal={true}>
        <TouchableOpacity
          style={styles.modalContainer}
          activeOpacity={1}
          onPressOut={() => setModalVisible(false)}
          accessibilityRole="button"
          accessibilityLabel="Close reaction picker">
          <View style={styles.modalContent}>
            {Object.entries(iconMapping).map(([key, { icon: Icon, color }]) => (
              <Pressable
                key={key}
                onPress={() => handleSelectReaction(key as ReactionType)}
                accessibilityRole="button"
                accessibilityLabel={`React with ${key}`}
                accessibilityHint={`Add ${key} reaction to this post`}
                style={({ pressed }) => ({
                  opacity: pressed ? 0.7 : 1,
                  padding: theme.spacing.spacing.s2,
                  borderRadius: 8,
                })}>
                <Icon size={32} color={color} strokeWidth={2.5} />
              </Pressable>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

export default React.memo(Reactions);
