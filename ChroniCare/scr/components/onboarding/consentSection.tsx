import React, { useMemo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../context/themeContext';
import ConsentCard from './consentCard';

interface ConsentItem {
  key: string;
  text: string;
  isChecked: boolean;
  onAgreeChange: (isAgreed: boolean) => void;
}

interface ConsentSectionProps {
  title: string;
  items: ConsentItem[];
}

const ConsentSection: React.FC<ConsentSectionProps> = ({
  title,
  items,
}) => {
  const { theme } = useTheme();

  const styles = useMemo(() => StyleSheet.create({
    container: {
      backgroundColor: theme.colors.Background.background0,
      borderRadius: theme.spacing.borderRadius.lg,
      padding: theme.spacing.spacing.s2,
      gap: theme.spacing.spacing.s2,
      shadowColor: theme.colors.Text.text900,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 4,
    },
    title: {
      ...theme.textVariants.text('md', 'semibold'),
      color: theme.colors.Text.text900,
    },
    itemsContainer: {
      gap: theme.spacing.spacing.s2,
    },
  }), [theme]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      <View style={styles.itemsContainer}>
        {items.map((item) => (
          <ConsentCard
            key={item.key}
            text={item.text}
            onAgreeChange={item.onAgreeChange}
            isChecked={item.isChecked}
          />
        ))}
      </View>
    </View>
  );
};

export default ConsentSection; 