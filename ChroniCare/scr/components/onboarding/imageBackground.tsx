import React from 'react';
import { ImageBackground, StyleSheet, View, ViewStyle } from 'react-native';
import { useTheme } from '../../context/themeContext';

interface ImageBackgroundProps {
  children?: React.ReactNode;
  style?: ViewStyle;
  imageStyle?: any;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
}

export const ImageBackgroundComponent: React.FC<ImageBackgroundProps> = ({ 
  children, 
  style,
  imageStyle,
  resizeMode = 'cover'
}) => {
  const { isDark } = useTheme();
  
  const backgroundSource = isDark 
    ? require('../../../assets/images/darkBackground.png')
    : require('../../../assets/images/lightBackground.png');

  return (
    <ImageBackground
      source={backgroundSource}
      style={[styles.container, style]}
      imageStyle={[styles.image, imageStyle]}
      resizeMode={resizeMode}
    >
      {children && (
        <View style={styles.contentContainer}>
          {children}
        </View>
      )}
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  image: {
    opacity: 1,
  },
  contentContainer: {
    flex: 1,
  },
});

export default ImageBackgroundComponent;
