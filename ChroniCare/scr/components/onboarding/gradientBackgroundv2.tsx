import React, { useMemo } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../../context/themeContext';
import { BlurView } from 'expo-blur';

interface GradientBackgroundV2Props {
  style?: ViewStyle;
  children?: React.ReactNode;
}

const GradientBackgroundV2: React.FC<GradientBackgroundV2Props> = ({
  style,
  children,
}) => {
  const { theme } = useTheme();

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.Background.background0,
    },
    blurView: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      width: '100%',
      height: '100%',
    },
    gradientEllipse1: {
      position: 'absolute',
      width: 200,
      height: 400,
      borderRadius: 0,
      top: 200,
      right: 200,
      opacity: 100,
      transform: [{ rotate: '45deg' }, {skewX: '20deg'}],
    },
    gradientEllipse2: {
      position: 'absolute',
      width: 300,
      height: 300,
      borderRadius: 150,
      bottom: -50,
      left: 0,
      opacity: 1,
    },
    gradientEllipse3: {
      position: 'absolute',
      width: 450,
      height: 150,
      borderRadius: 1000,
      top: 250,
      right: -100,
      opacity: 1,
      transform: [{ rotate: '45deg' }, {skewX: '20deg'}],
    },
  }), [theme]);

  return (
    <View style={[styles.container, style]}>
      {/* Gradient Ellipses (rendered first) */}
      <LinearGradient
        colors={[theme.colors.Primary.primary200, theme.colors.Primary.primary50]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientEllipse1}
      />
      <LinearGradient
        colors={[theme.colors.Tertiary.tertiary200, theme.colors.Tertiary.tertiary100]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientEllipse2}
      />
      <LinearGradient
        colors={[theme.colors.Secondary.secondary300, theme.colors.Secondary.secondary0]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientEllipse3}
      />

      {/* BlurView overlays the ellipses to create the blur effect */}
      <BlurView intensity={0} style={styles.blurView} />

      {/* Children are rendered on top of the blurred background */}
      {children}
    </View>
  );
};

export default GradientBackgroundV2;
