import React from 'react'
import { View, Text, StyleSheet, ViewStyle } from 'react-native'
import { useTheme } from '../../context/themeContext'

type TooltipVariant = 'top-center' | 'right-top'

type TooltipProps = {
  text: string
  variant?: TooltipVariant
  style?: ViewStyle
}

export default function Tooltip({ text, variant = 'top-center', style }: TooltipProps) {
  const { theme } = useTheme()

  const renderTooltip = () => {
    switch (variant) {
      case 'right-top':
        return (
          <View style={styles.wrapperRightTop}>
            {/* Bubble */}
            <View
              style={[
                styles.bubble,
                { backgroundColor: theme.colors.Text.text900 },
                style,
              ]}
            >
              <Text style={[theme.textVariants.body, { color: theme.colors.Text.text50, textAlign: 'center' }]}>
                {text}
              </Text>
            </View>
            {/* Arrow pointing up on the right side */}
            <View
              style={[
                styles.arrowRightTop,
                {
                  borderLeftColor: 'transparent',
                  borderRightColor: 'transparent',
                  borderBottomColor: theme.colors.Text.text900,
                },
              ]}
            />
          </View>
        )
      
      case 'top-center':
      default:
        return (
          <View style={styles.wrapper}>
            {/* Arrow pointing up */}
            <View
              style={[
                styles.arrow,
                {
                  borderLeftColor: 'transparent',
                  borderRightColor: 'transparent',
                  borderBottomColor: theme.colors.Text.text900,
                },
              ]}
            />
            {/* Bubble */}
            <View
              style={[
                styles.bubble,
                { backgroundColor: theme.colors.Text.text900 },
                style,
              ]}
            >
              <Text style={[theme.textVariants.body, { color: theme.colors.Text.text50, textAlign: 'center' }]}>
                {text}
              </Text>
            </View>
          </View>
        )
    }
  }

  return renderTooltip()
}

const ARROW_SIZE = 8

const styles = StyleSheet.create({
  wrapper: {
    alignItems: 'center',    // center arrow over bubble
  },
  wrapperRightTop: {
    alignItems: 'flex-start',
  },
  arrow: {
    width: 0,
    height: 0,
    borderLeftWidth: ARROW_SIZE,
    borderRightWidth: ARROW_SIZE,
    borderBottomWidth: ARROW_SIZE,
  },
  arrowRightTop: {
    width: 0,
    height: 0,
    borderLeftWidth: ARROW_SIZE,
    borderRightWidth: ARROW_SIZE,
    borderBottomWidth: ARROW_SIZE,
    position: 'absolute',
    top: -ARROW_SIZE,
    right: 10, // 10px padding from the right edge
  },
  bubble: {
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 6,
    maxWidth: 180,
    shadowColor: '#9FA6B2',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 2,
    // (optional) elevation for Android:
    elevation: 3,
  },
})