import React, { useMemo } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Check } from 'lucide-react-native';
import { useTheme } from '../../context/themeContext';

interface ConsentBoxProps {
  isChecked: boolean;
  onPress: () => void;
  text: string;
  disabled?: boolean;
}

const ConsentBox: React.FC<ConsentBoxProps> = ({ 
  isChecked, 
  onPress, 
  text, 
  disabled = false 
}) => {
  const { theme } = useTheme();

  const styles = useMemo(() => StyleSheet.create({
    container: {
      borderWidth: theme.spacing.borderWidth.s1,
      borderColor: theme.colors.Background.background300,
      borderRadius: theme.spacing.borderRadius.lg,
      padding: theme.spacing.spacing.s2,
      backgroundColor: theme.colors.Background.background0,
    },
    checkboxContainer: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      gap: theme.spacing.spacing.s3,
    },
    checkbox: {
      width: 16,
      height: 16,
      borderWidth: theme.spacing.borderWidth.s2,
      borderColor: theme.colors.Text.text400,
      borderRadius: theme.spacing.borderRadius.sm,
      backgroundColor: isChecked ? theme.colors.Primary.primary500 : 'transparent',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 2, // Slight adjustment to align with text baseline
    },
    checkboxChecked: {
      borderColor: theme.colors.Primary.primary500,
    },

    textContainer: {
      flex: 1,
    },
    text: {
      ...theme.textVariants.text('sm', 'regular'),
      color: theme.colors.Text.text600,
      lineHeight: 21,
    },
    disabled: {
      opacity: 0.6,
    },
  }), [theme, isChecked]);

  return (
    <TouchableOpacity 
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.7}
      style={[styles.container, disabled && styles.disabled]}
      accessibilityRole="checkbox"
      accessibilityState={{ checked: isChecked, disabled }}
      accessibilityLabel={text}
    >
      <View style={styles.checkboxContainer}>
        <View style={[styles.checkbox, isChecked && styles.checkboxChecked]}>
          {isChecked && (
            <Check size={10} color={theme.colors.Background.background0} />
          )}
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.text}>{text}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default ConsentBox;
