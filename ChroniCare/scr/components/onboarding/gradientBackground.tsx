import React from 'react';
import { StyleSheet, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../../context/themeContext';

interface GradientBackgroundProps {
  startColor?: string;
  endColor?: string;
  style?: ViewStyle;
  children?: React.ReactNode;
}

const GradientBackground: React.FC<GradientBackgroundProps> = ({
  startColor,
  endColor,
  style,
  children,
}) => {
  const { theme } = useTheme();

  // Use provided colors or default to theme colors
  const finalStartColor = startColor || theme.colors.Background.background0;
  const finalEndColor = endColor || theme.colors.Primary.primary300;

  return (
    <LinearGradient
      colors={[finalStartColor, finalEndColor]}
      start={{ x: 0.5, y: 0 }}  // Start from top center
      end={{ x: 0.5, y: 1 }}    // End at bottom center
      style={[styles.container, style]}
      locations={[0.5, 1]}
    >
      {children}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default GradientBackground;
