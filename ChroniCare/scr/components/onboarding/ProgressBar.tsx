import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useAnimatedStyle,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import { useTheme } from '../../context/themeContext';

interface ProgressBarProps {
  progress: number; // 0 to 100
  height?: number;
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');

const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  height = 6,
}) => {
  const { theme } = useTheme();

  const animatedStyle = useAnimatedStyle(() => {
    const progressWidth = (Math.min(Math.max(progress, 0), 100) / 100) * (SCREEN_WIDTH / 2);
    
    return {
      width: withTiming(progressWidth, {
        duration: 400,
        easing: Easing.out(Easing.cubic),
      }),
    };
  });

  const styles = StyleSheet.create({
    container: {
      width: SCREEN_WIDTH / 2,
      height,
      backgroundColor: theme.colors.Primary.primary300,
      borderRadius: height / 2,
      overflow: 'hidden',
    },
    progress: {
      height: '100%',
      backgroundColor: theme.colors.Primary.primary500,
      borderRadius: height / 2,
    },
  });

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.progress, animatedStyle]} />
    </View>
  );
};

export default ProgressBar; 