import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { SymbolView } from 'expo-symbols';
import { useTheme } from '../../context/themeContext';

interface ConsentCardProps {
  text: string;
  onAgreeChange: (isAgreed: boolean) => void;
  isChecked?: boolean; // Optional prop to control checked state externally
}

const ConsentCard: React.FC<ConsentCardProps> = ({
  text,
  onAgreeChange,
  isChecked,
}) => {
  // Use internal state only if isChecked prop is not provided
  const [internalIsAgreed, setInternalIsAgreed] = useState(false);
  const { theme } = useTheme();

  // Use external isChecked prop if provided, otherwise use internal state
  const isAgreed = isChecked !== undefined ? isChecked : internalIsAgreed;

  const styles = useMemo(() => StyleSheet.create({
    container: {
      backgroundColor: 'transparent',
      borderRadius: theme.spacing.borderRadius.lg,
      padding: theme.spacing.spacing.s0,
    },
    touchableRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    checkboxContainer: {
      width: 20,
      height: 20,
      borderRadius: 4,
      borderWidth: 2,
      borderColor: isAgreed ? theme.colors.Primary.primary500 : theme.colors.Text.text300,
      backgroundColor: isAgreed ? theme.colors.Primary.primary500 : 'transparent',
      marginRight: theme.spacing.spacing.s3,
      justifyContent: 'center',
      alignItems: 'center',
    },
    text: {
      ...theme.textVariants.text('sm', 'regular'),
      color: isAgreed ? theme.colors.Text.text900 : theme.colors.Text.text600,
      flex: 1,
      lineHeight: 21,
    },
  }), [theme, isAgreed]);

  const handlePress = () => {
    const newAgreeState = !isAgreed;
    
    // Update internal state only if we're managing state internally
    if (isChecked === undefined) {
      setInternalIsAgreed(newAgreeState);
    }
    
    // Always call the callback
    onAgreeChange(newAgreeState);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={handlePress} style={styles.touchableRow}>
        <View style={styles.checkboxContainer}>
          {isAgreed && (
            <SymbolView 
              name="checkmark" 
              size={12} 
              type="hierarchical" 
              tintColor={theme.colors.Text.text0}
            />
          )}
        </View>
        <Text style={styles.text}>{text}</Text>
      </TouchableOpacity>
    </View>
  );
};

export default ConsentCard; 
