import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';


export default function MeshBackground({
  pinkColors = ['#FF75B5', '#FFA4CF'] as const,
  lightBlueColors = ['#D9E4FF', '#ECF2FF'] as const,
  darkBlueColors = ['#8EA2FF', '#5B6EFF'] as const,
  style,
  
  ...rest
}: {
  pinkColors?: readonly [string, string];
  lightBlueColors?: readonly [string, string];
  darkBlueColors?: readonly [string, string];
  style?: ViewStyle;
  [key: string]: any;
}) {
  return (
    <View style={[styles.container, style]} {...rest}>
      {/* pink blob */}
      <LinearGradient
        colors={pinkColors}
        start={{ x: 0.3, y: 0.0 }}
        end={{ x: 0.8, y: 0.5 }}
        style={[styles.blob, styles.pinkTransform]}
      />

      {/* light blue blob */}
      <LinearGradient
        colors={lightBlueColors}
        start={{ x: 0.1, y: 0.6 }}
        end={{ x: 0.6, y: 1.0 }}
        style={[styles.blob, styles.lightBlueTransform]}
      />

      {/* dark blue blob */}
      <LinearGradient
        colors={darkBlueColors}
        start={{ x: 0.4, y: 0.4 }}
        end={{ x: 1.0, y: 1.0 }}
        style={[styles.blob, styles.darkBlueTransform]}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#F8F9FF',
  },

  /**
   * Smaller circular blobs that create distinct shapes rather than covering the whole screen
   */
  blob: {
    position: 'absolute',
    width: 200,
    height: 300,
    borderRadius: 150,
    opacity: 0.6,
  },

  /** Placement / rotation tweaks for each blob */
  pinkTransform: {
    top: 0,
    right: -50,
    transform: [{ rotate: '30deg' }],
  },
  lightBlueTransform: {
    top: '30%',
    left: -80,
    transform: [{ rotate: '-45deg' }],
  },
  darkBlueTransform: {
    bottom: -80,
    right: -60,
    transform: [{ rotate: '60deg' }],
  },
});
