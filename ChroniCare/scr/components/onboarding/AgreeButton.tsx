import React, { useMemo } from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/themeContext';

interface AgreeButtonProps {
  isChecked: boolean;
  onPress: () => void;
  label: string;
}

const AgreeButton: React.FC<AgreeButtonProps> = ({ isChecked, onPress, label }) => {
  const { theme } = useTheme();

  const styles = useMemo(() => StyleSheet.create({
    buttonContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 0,
      height: 36,
      paddingHorizontal: theme.spacing.spacing.s3_5,
      borderWidth: theme.spacing.borderWidth.s1,
      borderColor: theme.colors.Background.background900,
      borderRadius: theme.spacing.borderRadius.xl2,
      backgroundColor: theme.colors.Background.background0,
    },
    icon: {
      marginRight: theme.spacing.spacing.s2_5,

    },
    label: {
      ...theme.textVariants.text('sm', 'medium'),
      color: theme.colors.Text.text950,
    },
  }), [theme]);

  return (
    <TouchableOpacity onPress={onPress} style={styles.buttonContainer}>
      <Ionicons
        name={isChecked ? 'checkbox' : 'checkbox-outline'}
        size={20}
        color={isChecked ? theme.colors.Primary.primary500 : theme.colors.Text.text500}
        style={styles.icon}
      />
      <Text style={styles.label}>{label}</Text>
    </TouchableOpacity>
  );
};

export default AgreeButton; 