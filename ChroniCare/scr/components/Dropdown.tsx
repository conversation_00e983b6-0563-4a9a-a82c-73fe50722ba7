import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { ChevronDown, ChevronUp } from 'lucide-react-native';
import { useTheme } from '../context/themeContext';

interface DropdownOption {
  label: string;
  value: string;
}

interface DropdownProps {
  label: string;
  placeholder: string;
  options: DropdownOption[];
  selectedValue?: string;
  onSelect: (value: string) => void;
  required?: boolean;
  error?: string;
}

export const Dropdown: React.FC<DropdownProps> = ({
  label,
  placeholder,
  options,
  selectedValue,
  onSelect,
  required = false,
  error,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const { theme } = useTheme();

  const selectedOption = options.find(option => option.value === selectedValue);

  const handleSelect = (value: string) => {
    onSelect(value);
    setIsOpen(false);
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const styles = StyleSheet.create({
    container: {
      width: '100%',
      position: 'relative',
      zIndex: 1000,
    },
    label: {
      ...theme.textVariants.text('sm', 'medium'),
      color: theme.colors.Text.text950,
    },
    required: {
      color: '#ff4757',
    },
    dropdownButton: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: theme.spacing.spacing.s2,
      paddingRight: theme.spacing.spacing.s2,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Text.text950,
      backgroundColor: 'transparent',
    },
    dropdownText: {
      ...theme.textVariants.text('sm', 'regular'),
      color: selectedValue ? theme.colors.Text.text950 : theme.colors.Text.text500,
    },
    chevronIcon: {
      marginLeft: 8,
    },
    dropdownList: {
      position: 'absolute',
      top: '100%',
      left: 0,
      right: 0,
      backgroundColor: theme.colors.Background.background50,
      borderRadius: theme.spacing.borderRadius.md,
      marginTop: 4,
      elevation: 5,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      zIndex: 1001,
      maxHeight: 200,
    },
    optionItem: {
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingVertical: theme.spacing.spacing.s3,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Background.background200,
    },
    lastOptionItem: {
      borderBottomWidth: 0,
    },
    selectedOption: {
      backgroundColor: theme.colors.Background.background100,
    },
    optionText: {
      fontSize: 16,
      color: theme.colors.Text.text950,
    },
    errorText: {
      color: theme.colors.Secondary.secondary500,
      fontSize: 14,
      marginTop: 4,
    },
  });

  return (
    <View style={styles.container}>
      <Text style={styles.label}>
        {label}
        {required && <Text style={styles.required}>*</Text>}
      </Text>
      
      <TouchableOpacity 
        style={styles.dropdownButton}
        onPress={toggleDropdown}
        activeOpacity={0.7}
      >
        <Text style={styles.dropdownText}>
          {selectedOption ? selectedOption.label : placeholder}
        </Text>
        {isOpen ? (
          <ChevronUp 
            size={20} 
            color={theme.colors.Text.text600}
            style={styles.chevronIcon}
          />
        ) : (
          <ChevronDown 
            size={20} 
            color={theme.colors.Text.text600}
            style={styles.chevronIcon}
          />
        )}
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}

      {isOpen && (
        <View style={styles.dropdownList}>
          {options.map((option, index) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.optionItem,
                index === options.length - 1 && styles.lastOptionItem,
                selectedValue === option.value && styles.selectedOption,
              ]}
              onPress={() => handleSelect(option.value)}
              activeOpacity={0.7}
            >
              <Text style={styles.optionText}>{option.label}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );
}; 