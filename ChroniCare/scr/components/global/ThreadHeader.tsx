import { View, Image, StyleSheet, TouchableOpacity, Text } from 'react-native';
import React, { useMemo, useState } from 'react';
import { useTheme } from '../../context/themeContext';
import { useUser } from '../../context/userContext';
import { SafeAreaView } from 'react-native-safe-area-context';
import ProfileMenu from './ProfileMenu';
import { ArrowLeft } from 'lucide-react-native';
import { useRouter } from 'expo-router';

const ThreadHeader = () => {
  const { theme } = useTheme();
  const { user } = useUser();
  const [isMenuVisible, setMenuVisible] = useState(false);
  const router = useRouter();

  const styles = useMemo(() => StyleSheet.create({
    safeArea: {
      justifyContent: 'center',
    },
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.spacing.s4,
      height: 60,
    },
   
  
  
    headerContainer: {
      flex: 1,
      alignItems: 'center',
    }
  }), [theme]);

  return (
    <SafeAreaView style={[styles.safeArea, { backgroundColor: theme.colors.Background.background0 }]} edges={['top']}>
      <View style={styles.container}>
        <TouchableOpacity 
          onPress={() => router.back()}
        >
          <ArrowLeft color={theme.colors.Text.text900} size={24} />
        </TouchableOpacity>
        
        <View style={styles.headerContainer}>
        
        </View>

        <View style={{ width: 40 }} /> 
      </View>
      <ProfileMenu isVisible={isMenuVisible} onClose={() => setMenuVisible(false)} />
    </SafeAreaView>
  );
};

export default ThreadHeader;