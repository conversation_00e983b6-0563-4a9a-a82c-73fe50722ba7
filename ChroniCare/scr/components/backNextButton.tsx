import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Button } from './button';
import { useTheme } from '../context/themeContext';
import { ArrowLeft, ArrowRight } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';

export interface BackNextButtonProps {
  onBackPress?: () => void;
  onNextPress?: () => void;
  nextInactive?: boolean;
  backTitle?: string;
  nextTitle?: string;
  disabled?: boolean;
  backIcon?: React.ReactElement;
  nextIcon?: React.ReactElement;
  nextBackgroundColor?: string;
}

export const BackNextButton: React.FC<BackNextButtonProps> = ({
  onBackPress,
  onNextPress,
  nextInactive = false,
  backTitle = 'Back',
  nextTitle = 'Next',
  disabled = false,
  backIcon,
  nextIcon,
  nextBackgroundColor,
}) => {
  const { theme } = useTheme();

  const handleNextPress = () => {
    if (!nextInactive && onNextPress) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onNextPress();
    }
  };

  const defaultBackIcon = <ArrowLeft size={18} color={theme.colors.Text.text950} />;
  const defaultNextIcon = <ArrowRight size={18} color={nextInactive ? theme.colors.Text.text500 : theme.colors.Text.text0} />;

  return (
    <View style={styles.container}>
      <Button
        title={backTitle}
        variant="outline"
        icon={backIcon || defaultBackIcon}
        iconPosition="left"
        onPress={onBackPress}
        disabled={disabled}
        style={styles.backButton}
      />
      
      <Button
        title={nextTitle}
        variant="iconNeutral"
        icon={nextIcon || defaultNextIcon}
        iconPosition="right"
        onPress={nextInactive ? undefined : handleNextPress}
        disabled={disabled || nextInactive}
        backgroundColor={nextBackgroundColor || (nextInactive ? theme.colors.Background.background300 : undefined)}
        textColor={nextInactive ? theme.colors.Text.text500 : undefined}
        style={styles.nextButton}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 16,
  },
  backButton: {
    flex: 1,
  },
  nextButton: {
    flex: 3,
  },
});

export default BackNextButton;
