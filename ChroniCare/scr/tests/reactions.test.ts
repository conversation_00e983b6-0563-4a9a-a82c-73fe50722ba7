import { renderHook, act } from '@testing-library/react-native';
import { MockedProvider } from '@apollo/client/testing';
import { useThreadReactions } from '../hooks/useThreadReactions';
import { useReactionDisplay } from '../hooks/useReactionDisplay';
import { ReactionCount } from '../graphql/fragments';

// Mock expo-haptics
jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  ImpactFeedbackStyle: {
    Light: 'light',
    Medium: 'medium',
    Heavy: 'heavy',
  },
}));

// Mock lodash debounce
jest.mock('lodash', () => ({
  debounce: (fn: any) => {
    fn.cancel = jest.fn();
    return fn;
  },
}));

describe('useReactionDisplay', () => {
  it('should calculate total reactions correctly', () => {
    const reactionCounts: ReactionCount = {
      love: 5,
      withYou: 3,
      funny: 2,
      insightful: 1,
      poop: 0,
    };

    const { result } = renderHook(() => useReactionDisplay(reactionCounts));

    expect(result.current.totalReactions).toBe(11);
    expect(result.current.displayedReactions).toHaveLength(4); // Only non-zero reactions
  });

  it('should handle null reaction counts', () => {
    const { result } = renderHook(() => useReactionDisplay(null));

    expect(result.current.totalReactions).toBe(0);
    expect(result.current.displayedReactions).toHaveLength(1); // Default love reaction
    expect(result.current.displayedReactions[0].key).toBe('love');
  });

  it('should sort reactions by count descending', () => {
    const reactionCounts: ReactionCount = {
      love: 1,
      withYou: 5,
      funny: 3,
      insightful: 2,
      poop: 0,
    };

    const { result } = renderHook(() => useReactionDisplay(reactionCounts));

    const sortedKeys = result.current.displayedReactions.map(r => r.key);
    expect(sortedKeys).toEqual(['withYou', 'funny', 'insightful']); // Top 3, sorted by count
  });
});

describe('useThreadReactions', () => {
  const mockThreadId = 'test-thread-id';

  it('should provide debounced addReaction function', () => {
    const mocks = [];

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <MockedProvider mocks={mocks} addTypename={false}>
        {children}
      </MockedProvider>
    );

    const { result } = renderHook(() => useThreadReactions(mockThreadId), { wrapper });

    expect(result.current.addReaction).toBeDefined();
    expect(result.current.loading).toBe(false);
    expect(result.current.errorMessage).toBeNull();
  });
});
