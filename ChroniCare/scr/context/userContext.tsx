import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { ApolloError, useQuery } from '@apollo/client';
import { GET_ME } from '../graphql/queries';
import { useAuth } from './authContext';

export interface User {
  id: string;
  firebaseUid: string;
  email: string | null;
  firstName: string | null;
  lastName: string | null;
  photoURL: string | null;
  onboardingCompleted: boolean;
  communities: {
    id: string;
    name: string;
  }[] | null;
}

interface UserContextType {
  user: User | null ;
  loading: boolean;
  error?: ApolloError;
  refetchUser: () => Promise<any>;
  setSignupFlowActive: (isActive: boolean) => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children }: { children: ReactNode }) {
  const { user: authUser, initializing: authInitializing } = useAuth();
  const [isSignupFlowActive, setSignupFlowActive] = useState(false);
  const { data, loading, refetch, error } = useQuery(GET_ME, {
    skip: !authUser || isSignupFlowActive, // Skip query if user is not authenticated or if signup is in progress
    errorPolicy: 'all',
  });

  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    if (data && data.me) {
      setUser(data.me);
    } else if (!authInitializing && !authUser) {
        // Clear user data on logout
        setUser(null);
    }
  }, [data, authUser, authInitializing]);

  const refetchUser = () => {
    return refetch();
  };

  return (
    <UserContext.Provider value={{ user, loading: authInitializing || loading, refetchUser, setSignupFlowActive, error }}>
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
} 