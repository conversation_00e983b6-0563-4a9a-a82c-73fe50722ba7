import React, { createContext, useContext } from 'react';
import { useUser } from './userContext';

interface OnboardingContextType {
  hasCompletedOnboarding: boolean;
  isLoading: boolean;
}

const OnboardingContext = createContext<OnboardingContextType>({
  hasCompletedOnboarding: false,
  isLoading: true,
});

interface OnboardingProviderProps {
  children: React.ReactNode;
}

export const OnboardingProvider: React.FC<OnboardingProviderProps> = ({ children }) => {
  const { user, loading: userLoading } = useUser();

  const hasCompletedOnboarding = user?.onboardingCompleted ?? false;

  return (
    <OnboardingContext.Provider
      value={{
        hasCompletedOnboarding,
        isLoading: userLoading,
      }}
    >
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = () => useContext(OnboardingContext);
