import React, { createContext, useContext, useState, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import { useFonts } from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';
import {
  Inter_300Light,
  Inter_400Regular,
  Inter_500Medium,
  Inter_600SemiBold,
  Inter_700Bold,
} from '@expo-google-fonts/inter';
import {
  Merriweather_300Light,
  Merriweather_400Regular,
  Merriweather_700Bold,
} from '@expo-google-fonts/merriweather';
import { lightTheme, darkTheme, Theme } from '../theme/theme';

// Prevent the splash screen from auto-hiding
SplashScreen.preventAutoHideAsync();

interface ThemeContextType {
  theme: Theme;
  isDark: boolean;
  toggleTheme: () => void;
  setThemeMode: (mode: 'light' | 'dark' | 'system') => void;
}

// Create the theme context
const ThemeContext = createContext<ThemeContextType>({
  theme: lightTheme,
  isDark: false,
  toggleTheme: () => {},
  setThemeMode: () => {},
});

// Theme mode type
type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Load Google Fonts
  const [fontsLoaded, fontError] = useFonts({
    Inter_300Light,
    Inter_400Regular,
    Inter_500Medium,
    Inter_600SemiBold,
    Inter_700Bold,
    Merriweather_300Light,
    Merriweather_400Regular,
    Merriweather_700Bold,
  });

  // Get system color scheme
  const systemColorScheme = useColorScheme();
  
  // State for the user's theme preference
  const [themeMode, setThemeMode] = useState<ThemeMode>('system');
  
  // Initialize isDark based on system preference
  const [isDark, setIsDark] = useState<boolean>(systemColorScheme === 'dark');

  // Hide splash screen when fonts are loaded
  useEffect(() => {
    if (fontsLoaded || fontError) {
      SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  // Log font loading errors
  useEffect(() => {
    if (fontError) {
      console.error('Font loading error:', fontError);
    }
  }, [fontError]);

  // Effect to update isDark when system color scheme changes
  useEffect(() => {
    if (themeMode === 'system') {
      setIsDark(systemColorScheme === 'dark');
    }
  }, [systemColorScheme, themeMode]);

  // Toggle between light and dark mode
  const toggleTheme = () => {
    
    // Always invert the current isDark value, regardless of mode
    setIsDark(prev => !prev);
    
    // If in system mode, switch to manual mode based on what we're switching to
    if (themeMode === 'system') {
    } else {
      // If in manual mode, just toggle between light and dark
    }
  };

  // Update theme mode
  const handleSetThemeMode = (mode: ThemeMode) => {
    setThemeMode(mode);
    
    if (mode === 'system') {
      setIsDark(systemColorScheme === 'dark');
    } else {
      setIsDark(mode === 'dark');
    }
  };

  // Current theme object - must be after all state updates
  const theme = isDark ? darkTheme : lightTheme;

  // Don't render until fonts are loaded
  if (!fontsLoaded && !fontError) {
    return null;
  }

  return (
    <ThemeContext.Provider
      value={{
        theme,
        isDark,
        toggleTheme,
        setThemeMode: handleSetThemeMode,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook for using the theme context
export const useTheme = () => useContext(ThemeContext); 