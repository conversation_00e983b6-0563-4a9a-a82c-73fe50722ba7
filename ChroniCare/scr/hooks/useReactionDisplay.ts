import { useMemo } from 'react';
import { ReactionCount } from '../graphql/fragments';
import { Heart, HeartHandshake, Laugh, Lightbulb, IceCream } from 'lucide-react-native';

type ReactionType = keyof ReactionCount;

const iconMapping: Record<
  ReactionType,
  { icon: React.ElementType; color: string; colorLight: string }
> = {
  love: { icon: Heart, color: '#FF6B6B', colorLight: '#FFD1D1' },
  withYou: { icon: HeartHandshake, color: '#4ECDC4', colorLight: '#C0FDFB' },
  funny: { icon: Laugh, color: '#FFCF5C', colorLight: '#FFF4D6' },
  insightful: { icon: Lightbulb, color: '#45B7D1', colorLight: '#C2EFFF' },
  poop: { icon: IceCream, color: '#C0A080', colorLight: '#EBE0D6' },
};

export const useReactionDisplay = (reactionCounts: ReactionCount | null) => {
  const totalReactions = useMemo(() => {
    if (!reactionCounts) return 0;
    
    // Ensure all values are numbers and sum them up
    const total = (reactionCounts.love || 0) + 
                  (reactionCounts.withYou || 0) + 
                  (reactionCounts.funny || 0) + 
                  (reactionCounts.insightful || 0) + 
                  (reactionCounts.poop || 0);
    
    return total;
  }, [reactionCounts]);

  const displayedReactions = useMemo(() => {
    if (!reactionCounts || totalReactions === 0) {
      return [{
        key: 'love' as ReactionType,
        count: 0,
        ...iconMapping['love'],
      }];
    }
    
    return (Object.entries(reactionCounts) as Array<[ReactionType, number]>)
      .map(([key, count]) => ({
        key,
        count: count || 0,
        ...iconMapping[key],
      }))
      .filter(reaction => reaction.count > 0)
      .sort((a, b) => b.count - a.count)
      .slice(0, 3);
  }, [reactionCounts, totalReactions]);

  return {
    totalReactions,
    displayedReactions,
    iconMapping,
  };
};
