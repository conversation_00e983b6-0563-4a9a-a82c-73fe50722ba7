import { useState, useEffect } from 'react';
import { useMutation } from '@apollo/client';
import { GET_ME, CREATE_PROFILE_IMAGE_UPLOAD_URL, UPDATE_USER_PROFILE_IMAGE, REMOVE_USER_PROFILE_IMAGE } from '../graphql/queries';
import { useUser, User } from '../context/userContext';

export interface UseProfilePictureResult {
  user: User | null;
  existingPhotoURL: string | null;
  isLoading: boolean;
  error: any;
  refreshProfilePicture: () => void;
  uploadProfilePicture: (imageUri: string, mimeType: string) => Promise<void>;
  removeProfilePicture: () => Promise<void>;
  isUploading: boolean;
  isRemoving: boolean;
}

interface UseProfilePictureOptions {
  onboardingStoreApi?: {
    setProfilePicture: (imageUri: string | null) => void;
  };
}

export const useProfilePicture = (options?: UseProfilePictureOptions): UseProfilePictureResult => {
  const { user, loading, error, refetchUser: refetch } = useUser();
  const setProfilePicture = options?.onboardingStoreApi?.setProfilePicture;
  const [hasSetInitialPhoto, setHasSetInitialPhoto] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);

  // Mutations for uploading profile picture
  const [createUploadUrl] = useMutation(CREATE_PROFILE_IMAGE_UPLOAD_URL);
  const [updateUserProfileImage] = useMutation(UPDATE_USER_PROFILE_IMAGE, {
    refetchQueries: [{ query: GET_ME }],
  });
  const [removeUserProfileImage] = useMutation(REMOVE_USER_PROFILE_IMAGE, {
    refetchQueries: [{ query: GET_ME }],
  });
  // Set initial profile picture from database if available
  useEffect(() => {
    if (user?.photoURL && !hasSetInitialPhoto && setProfilePicture) {
      setProfilePicture(user.photoURL);
      setHasSetInitialPhoto(true);
    }
  }, [user?.photoURL, setProfilePicture, hasSetInitialPhoto]);

  const refreshProfilePicture = () => {
    refetch();
  };

  const uploadProfilePicture = async (
    imageUri: string,
    mimeType: string,
  ): Promise<void> => {
    try {
      setIsUploading(true);

      // Step 1: Get signed upload URL from backend
      const { data: uploadData } = await createUploadUrl({
        variables: { contentType: mimeType },
      });
      const { signedUrl, publicUrl } = uploadData.createProfileImageUploadUrl;

      // Step 2: Upload image directly to Google Cloud Storage
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      // Log blob details for debugging
      console.log('Blob details:', { type: blob.type, size: blob.size });

      console.log('Uploading to Google Cloud Storage');
      const uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': mimeType,
        },
        body: blob,
      });

      console.log('Upload response status:', uploadResponse.status);
      console.log('Upload response headers:', Object.fromEntries(uploadResponse.headers.entries()));

      if (!uploadResponse.ok) {
        const responseText = await uploadResponse.text();
        console.error('Upload failed with status:', uploadResponse.status);
        console.error('Upload response body:', responseText);
        throw new Error(`Failed to upload image to storage: ${uploadResponse.status} - ${responseText}`);
      }

      // Step 3: Update user profile with the public URL
      console.log('Updating user profile with new image URL');
      await updateUserProfileImage({
        variables: { profileImageUrl: publicUrl },
      });

      // Update local state
      console.log('Updating local state with new image URL');
      if (setProfilePicture) {
        setProfilePicture(publicUrl);
      }

      console.log('Profile picture uploaded successfully:', publicUrl);
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      throw error;
    } finally {
      setIsUploading(false);
      console.log('Upload process completed');
    }
  };

  const removeProfilePicture = async (): Promise<void> => {
    try {
      setIsRemoving(true);

      // Remove profile picture from backend
      await removeUserProfileImage();

      // Update local state
      console.log('Profile picture removed successfully');
      if (setProfilePicture) {
        setProfilePicture(null);
      }
    } catch (error) {
      console.error('Error removing profile picture:', error);
      throw error;
    } finally {
      setIsRemoving(false);
    }
  };

  return {
    user,
    existingPhotoURL: user?.photoURL || null,
    isLoading: loading,
    error,
    refreshProfilePicture,
    uploadProfilePicture,
    removeProfilePicture,
    isUploading,
    isRemoving,
  };
}; 

