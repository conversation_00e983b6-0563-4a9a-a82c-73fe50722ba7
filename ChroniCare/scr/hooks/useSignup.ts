import { useState } from 'react';
import { useMutation } from '@apollo/client';
import { Alert } from 'react-native';
import { useAuth } from '../context/authContext';
import { useUser } from '../context/userContext';
import { CREATE_USER } from '../graphql/queries';

export const useSignup = () => {
  const { signUp } = useAuth();
  const { refetchUser, setSignupFlowActive } = useUser();
  const [isSigningUp, setIsSigningUp] = useState(false);

  const [createUser] = useMutation(CREATE_USER, {
    onError: (error) => {
      console.error('Error creating user in database:', error);
      Alert.alert(
        'Account Partially Created',
        'Your account was created, but we failed to save your details. Please contact support.'
      );
    },
  });

  const handleSignUp = async (email: string, password: string) => {
    setIsSigningUp(true);
    setSignupFlowActive(true);
    try {
      const userCredential = await signUp(email, password);

      if (userCredential && userCredential.user) {
        const firebaseUser = userCredential.user;
        
        await createUser({
          variables: {
            input: {
              firebaseUid: firebaseUser.uid,
              email: firebaseUser.email || email,
              displayName: firebaseUser.displayName,
              emailVerified: firebaseUser.emailVerified,
              photoURL: firebaseUser.photoURL,
              onboardingCompleted: false,
            },
          },
        });
        
        await refetchUser();
      }
    } catch (error) {
      // This error is typically from the signUp function, which already alerts the user.
      // We log it here for debugging purposes.
      console.error('Signup process failed:', error);
    } finally {
      setIsSigningUp(false);
      setSignupFlowActive(false);
    }
  };

  return { handleSignUp, isSigningUp };
}; 