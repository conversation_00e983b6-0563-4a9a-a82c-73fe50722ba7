import { useMutation, useApolloClient, gql } from '@apollo/client';
import { THREAD_FRAGMENT, ReactionCount, Thread } from '../graphql/fragments';
import { useState, useCallback, useMemo } from 'react';
import { debounce } from 'lodash';

// TODO: This mutation needs to be implemented in your backend
const ADD_REACTION = gql`
  mutation AddReaction($threadId: ID!, $reaction: String!) {
    addReaction(threadId: $threadId, reaction: $reaction) {
      _id
      reactionCounts {
        love
        withYou
        funny
        insightful
        poop
      }
      myReaction
    }
  }
`;

type ReactionType = keyof ReactionCount;

interface AddReactionResult {
  addReaction: {
    _id: string;
    reactionCounts: ReactionCount;
    myReaction: string | null;
  };
}

export const useThreadReactions = (threadId: string) => {
  // Get current thread data for optimistic response
  const { data: threadData } = useQuery<{ threads: Thread[] }>(GET_THREADS, {
    variables: { limit: 10, communityId: undefined },
    fetchPolicy: 'cache-only', // Only read from cache, don't make network request
  });

  const currentThread = threadData?.threads?.find((t) => t._id === threadId);

  const [addReactionMutation, { loading, error }] = useMutation<AddReactionResult>(ADD_REACTION, {
    optimisticResponse: (variables) => {
      if (!currentThread) {
        // Cannot perform optimistic update if the thread is not in cache
        return {
          addReaction: {
            _id: variables.threadId,
            reactionCounts: {
              love: 0, withYou: 0, funny: 0, insightful: 0, poop: 0,
            },
            myReaction: variables.reaction,
          },
        };
      }

      const newReaction = variables.reaction as ReactionType;
      const oldReaction = currentThread.myReaction as ReactionType | null;
      const newCounts = { ...currentThread.reactionCounts };

      if (oldReaction) {
        // Decrement the old reaction count
        newCounts[oldReaction] = (newCounts[oldReaction] || 1) - 1;
      }
      
      // If the new reaction is different from the old one, increment it
      if (oldReaction !== newReaction) {
        newCounts[newReaction] = (newCounts[newReaction] || 0) + 1;
      }

      return {
        addReaction: {
          _id: variables.threadId,
          reactionCounts: newCounts,
          myReaction: oldReaction === newReaction ? null : newReaction,
        },
      };
    },
    update: (cache, { data }) => {
      if (!data?.addReaction) return;

      // Update all relevant queries in cache
      cache.modify({
        id: cache.identify({ __typename: 'Thread', _id: threadId }),
        fields: {
          reactionCounts: () => data.addReaction.reactionCounts,
          myReaction: () => data.addReaction.myReaction,
        },
      });
    },
    onError: (error) => {
      console.error('Failed to add reaction:', error);
      // Could show user-friendly error message here
    },
  });

  const addReaction = async (reaction: ReactionType) => {
    if (loading) return; // Prevent concurrent requests
    
    try {
      await addReactionMutation({
        variables: { threadId, reaction },
      });
    } catch (err) {
      // Error is handled by onError callback
      console.error('Reaction failed:', err);
    }
  };

  return {
    addReaction,
    loading,
    error,
  };
}; 