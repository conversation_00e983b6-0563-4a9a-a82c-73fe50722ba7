{"expo": {"name": "ChroniCare", "slug": "ChroniCare", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "chronicare", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "io.chronicare.ChroniCare", "googleServicesFile": "./GoogleService-Info.plist", "associatedDomains": ["applinks:chronicare10.firebaseapp.com", "applinks:chronicare10.web.app"]}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "googleServicesFile": "./google-services.json", "edgeToEdgeEnabled": true, "package": "io.chronicare.ChroniCare", "intentFilters": [{"action": "VIEW", "category": ["BROWSABLE", "DEFAULT"], "data": [{"scheme": "https", "host": "chronicare10.firebaseapp.com", "pathPrefix": "/__/auth/"}, {"scheme": "https", "host": "chronicare10.web.app", "pathPrefix": "/__/auth/"}, {"scheme": "chronicare"}]}]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-font", "@react-native-firebase/app", "@react-native-firebase/auth", ["@react-native-google-signin/google-signin", {"iosUrlScheme": "com.googleusercontent.apps.1056297800349-sp8htepcij1fu64i395tei5gqur401sc"}], ["expo-build-properties", {"ios": {"useFrameworks": "static"}}]], "experiments": {"typedRoutes": true}}}