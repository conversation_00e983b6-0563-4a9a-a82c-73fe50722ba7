import React, { useState, useMemo, useRef, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  Platform,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { CircleUserRound, Info } from 'lucide-react-native';
import { useRouter, useFocusEffect } from 'expo-router';
import { BottomSheetModalProvider, BottomSheetModal, BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { useTheme } from '../../../scr/context/themeContext';
import { BackNextButton } from '../../../scr/components/backNextButton';
import { Dropdown } from '../../../scr/components/Dropdown';
import { CountryPicker } from '../../../scr/components/CountryPicker';
import { Tooltip } from '@rneui/themed';
import { getLocal } from '../../../scr/utils/getLocal';
import countries from 'i18n-iso-countries';
import enJson from 'i18n-iso-countries/langs/en.json';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { usePersonalInfoStore, useOnboardingStore } from '../../../scr/utils/onboardingStore';
import { ImageBackgroundComponent } from '../../../scr/components/onboarding/imageBackground';

const InfoScreen = () => {
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  
  const router = useRouter();
  const { theme } = useTheme();

  // Use the onboarding store for personal info management
  const { personalInfo, updatePersonalInfo, setPersonalInfoComplete, isPersonalInfoValid } = usePersonalInfoStore();
  const setCurrentStep = useOnboardingStore((state) => state.setCurrentStep);

  // Set current step when screen comes into focus (including back navigation)
  useFocusEffect(
    React.useCallback(() => {
      setCurrentStep(2); // Info is step 2 (after name)
    }, [setCurrentStep])
  );
  
  const displayName = personalInfo.firstName?.split(' ')[0] || '';
  
  // Bottom sheet ref and snap points
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['50%'], []);

  // Backdrop render function
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  countries.registerLocale(enJson);
  type CountryItem = {
    code: string;
    names: {
      en: string;      // e.g. "Denmark"
      local: string;   // e.g. "Danmark" (if user locale is "da")
    };
  };

  const local = getLocal();
  const localLanguage = local.languageCode;
  const localRegion = local.regionCode;

  // Gender options
  const genderOptions = [
    { label: 'Female', value: 'female' },
    { label: 'Male', value: 'male' },
    { label: 'Non-binary', value: 'non-binary' },
    { label: 'Prefer not to say', value: 'prefer-not-to-say' },
  ];

  // Create theme-aware StyleSheet
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoidingContainer: {
      flex: 1,
      paddingBottom: 100,
    },
    content: {
      flex: 1,
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingBottom: 30,
    },
    mainContent: {
      flex: 1,
      justifyContent: 'flex-end',
      alignItems: 'stretch',
    },
    centeredPageContentWrapper: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'stretch',
      gap: theme.spacing.spacing.s2,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      width: '100%',
    },
    profileIcon: {
      alignSelf: 'center',
    },
    infoIcon: {
      alignSelf: 'flex-end',
    },
    title: {
      ...theme.textVariants.heading('xl', 'bold'),
      color: theme.colors.Text.text950,
      alignSelf: 'flex-start',
    },
    titleContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      width: '100%',
      paddingBottom: theme.spacing.spacing.s3,
    },
    inputContainer: {
      width: '100%',
      gap: 6,
    },
    label: {
      ...theme.textVariants.text('sm', 'medium'),
      color: theme.colors.Text.text950,
    },
    required: {
      color: '#ff4757',
    },
    inputWrapper: {
      position: 'relative',
      width: '100%',
    },
    input: {
      fontSize: theme.typography.textSize.sm,
      color: theme.colors.Text.text950,
      paddingVertical: theme.spacing.spacing.s2,
      paddingRight: theme.spacing.spacing.s4,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Text.text950,
      backgroundColor: 'transparent',
    },
    errorText: {
      color: theme.colors.Secondary.secondary500,
      fontSize: theme.typography.textSize.xs,
      marginTop: theme.spacing.spacing.s1,
    },
    // Bottom Sheet Date Picker Styles
    datePickerContainer: {
      backgroundColor: theme.colors.Background.background0,
      paddingVertical: theme.spacing.spacing.s4,
      paddingHorizontal: theme.spacing.spacing.s4,
      flex: 1,
    },
    datePickerHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: theme.spacing.spacing.s4,
      paddingHorizontal: theme.spacing.spacing.s1,
    },
    datePickerTitle: {
      ...theme.textVariants.text('lg', 'semibold'),
      color: theme.colors.Text.text950,
    },
    doneButton: {
      backgroundColor: theme.colors.Background.background800,
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingVertical: theme.spacing.spacing.s2,
      borderRadius: theme.spacing.borderRadius.xl,
    },
    doneButtonText: {
      ...theme.textVariants.text('sm', 'medium'),
      color: theme.colors.Text.text0,
    },
  }), [theme]);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    // Handle date selection
    if (selectedDate && event.type !== 'dismissed') {
      updatePersonalInfo({ birthdate: selectedDate });
    }
    
    // Only close the picker on Android (automatic behavior) or when explicitly dismissed
    if (Platform.OS === 'android' || event.type === 'dismissed') {
      setShowDatePicker(false);
    }
    
    // On iOS with spinner mode, keep the picker open until user taps outside
    // The picker will stay open and continue to update the date as user scrolls
  };

  const showDatepicker = () => {
    if (Platform.OS === 'ios') {
      bottomSheetModalRef.current?.present();
    } else {
      setShowDatePicker(true);
    }
  };

  // Function to handle tapping outside the date picker on iOS
  const handleBackdropPress = useCallback(() => {
    if (Platform.OS === 'ios') {
      bottomSheetModalRef.current?.dismiss();
    } else {
      setShowDatePicker(false);
    }
  }, []);

  const handleBack = () => {
    router.back();
  };

  const handleNext = () => {
    setHasAttemptedSubmit(true);
    if (!isPersonalInfoValid()) {
      return;
    }

    // Mark personal info as complete in the store
    setPersonalInfoComplete();
    console.log('Personal info data:', personalInfo);
    router.push('/(auth)/(onboarding)/profilePicture');
  };

  const handleGenderSelect = (value: string) => {
    updatePersonalInfo({ gender: value });
  };

  const handleCountrySelect = (countryCode: string, countryDisplayName: string) => {
    updatePersonalInfo({ 
      countryCode: countryCode,
      countryName: countryDisplayName 
    });
  };

  // Calculate maximum date (today) and minimum date (reasonable birth year)
  const maxDate = new Date();
  const minDate = new Date();
  minDate.setFullYear(1900);

  return (
    <BottomSheetModalProvider>
      <ImageBackgroundComponent style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <KeyboardAvoidingView
            behavior="padding"
            style={styles.keyboardAvoidingContainer}
          >
            <View style={styles.content}>
              <View style={styles.mainContent}>
                <View style={styles.centeredPageContentWrapper}>
                  <View style={styles.header}>
                    <CircleUserRound 
                      size={32} 
                      color={theme.colors.Text.text950}
                      style={styles.profileIcon}
                    />
                  </View>
                  <View style={styles.titleContainer}>
                    <Text style={styles.title}>
                      Nice to meet you{displayName && ', '}
                      <Text style={{ color: theme.colors.Primary.primary500 }}>
                        {displayName}
                      </Text>
                    </Text>
                    <View style={styles.infoIcon}>
                      <Tooltip
                        visible={showTooltip}
                        onOpen={() => setShowTooltip(true)}
                        onClose={() => setShowTooltip(false)}
                        popover={
                          <View style={{ maxWidth: 220 }}>
                            <Text style={{
                              color: theme.colors.Text.text0,
                              fontSize: theme.typography.textSize.sm,
                              textAlign: 'left',
                              lineHeight: theme.typography.textSize.sm * 1.4,
                              flexWrap: 'wrap',
                            }}>
                              Only your name is visible to other users. You can always choose to interact anonymously.
                            </Text>
                          </View>
                        }
                        width={250}
                        height={90}
                        backgroundColor={theme.colors.Background.background800}
                        withOverlay={true}
                        overlayColor="rgba(0,0,0,0.3)"
                        skipAndroidStatusBar={true}
                        containerStyle={{
                          borderRadius: theme.spacing.borderRadius.md,
                          padding: theme.spacing.spacing.s3,
                          shadowColor: '#000',
                          shadowOffset: { width: 0, height: 2 },
                          shadowOpacity: 0.25,
                          shadowRadius: 4,
                          elevation: 5,
                        }}
                        toggleOnPress={true}
                        withPointer={false}
                      >
                        <View>
                          <Info 
                            size={24} 
                            color={theme.colors.Text.text950}
                          />
                        </View>
                      </Tooltip>
                    </View>
                  </View>

                  <View style={styles.inputContainer}>
                    <Text style={styles.label}>
                      Birthdate<Text style={styles.required}>*</Text>
                    </Text>
                    <TouchableOpacity onPress={showDatepicker}>
                      <View style={styles.input}>
                        <Text style={[
                          { ...theme.textVariants.text('sm', 'regular'), color: theme.colors.Text.text500 },
                          personalInfo.birthdate && { color: theme.colors.Text.text950 }
                        ]}>
                          {personalInfo.birthdate ? formatDate(personalInfo.birthdate) : 'Press to select'}
                        </Text>
                      </View>
                    </TouchableOpacity>
                    {hasAttemptedSubmit && !personalInfo.birthdate && (
                      <Text style={styles.errorText}>Birthdate is required</Text>
                    )}
                  </View>

                  <View style={styles.inputContainer}>
                    <Dropdown
                      label="Gender"
                      placeholder="Press to select"
                      options={genderOptions}
                      selectedValue={personalInfo.gender}
                      onSelect={handleGenderSelect}
                      required
                      error={hasAttemptedSubmit && personalInfo.gender === '' ? 'Gender is required' : undefined}
                    />
                  </View>
                  <View style={styles.inputContainer}>
                    <CountryPicker
                      label="Country of residency"
                      placeholder="Press to select"
                      selectedValue={personalInfo.countryCode}
                      onSelect={handleCountrySelect}
                      required
                      error={hasAttemptedSubmit && personalInfo.countryCode === '' ? 'Country of residency is required' : undefined}
                      localLanguageCode={localLanguage || 'en'}
                    />
                  </View>
                </View>

                <BackNextButton
                  onBackPress={handleBack}
                  onNextPress={handleNext}
                  nextInactive={!isPersonalInfoValid()}
                  disabled={false}
                />
              </View>
            </View>
          </KeyboardAvoidingView>

          {showDatePicker && Platform.OS === 'android' && (
            <DateTimePicker
              testID="dateTimePicker"
              value={personalInfo.birthdate || new Date()}
              mode="date"
              is24Hour={true}
              display="default"
              onChange={handleDateChange}
              maximumDate={maxDate}
              minimumDate={minDate}
              textColor={theme.colors.Text.text950}
            />
          )}
          
          {Platform.OS === 'ios' && (
            <BottomSheetModal
              ref={bottomSheetModalRef}
              snapPoints={snapPoints}
              enablePanDownToClose={true}
              onDismiss={() => setShowDatePicker(false)}
              backgroundStyle={{ backgroundColor: theme.colors.Background.background0 }}
              backdropComponent={renderBackdrop}
              handleIndicatorStyle={{
                backgroundColor: theme.colors.Background.background900,
              }}
            >
              <BottomSheetView style={styles.datePickerContainer}>
                <View style={styles.datePickerHeader}>
                  <Text style={styles.datePickerTitle}>Select Birthdate</Text>
                  <TouchableOpacity style={styles.doneButton} onPress={handleBackdropPress}>
                    <Text style={styles.doneButtonText}>Done</Text>
                  </TouchableOpacity>
                </View>
                <DateTimePicker
                  testID="dateTimePicker"
                  value={personalInfo.birthdate || new Date()}
                  mode="date"
                  is24Hour={true}
                  display="spinner"
                  onChange={handleDateChange}
                  maximumDate={maxDate}
                  minimumDate={minDate}
                  textColor={theme.colors.Text.text950}
                  style={{ backgroundColor: 'transparent' }}
                />
              </BottomSheetView>
            </BottomSheetModal>
          )}
        </SafeAreaView>
      </ImageBackgroundComponent>
    </BottomSheetModalProvider>
  );
};

export default InfoScreen;
