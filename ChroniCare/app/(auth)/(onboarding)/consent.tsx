import React, { useMemo, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { ArrowRight, FileCheck } from 'lucide-react-native';
import { useRouter, useFocusEffect } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  Easing,
} from 'react-native-reanimated';
import { useTheme } from '@/scr/context/themeContext';
import { BackNextButton } from '@/scr/components/backNextButton';
import ConsentSection from '@/scr/components/onboarding/consentSection';
import { useConsentStore, useOnboardingStore } from '@/scr/utils/onboardingStore';
import { ImageBackgroundComponent } from '@/scr/components/onboarding/imageBackground';
import { Button } from '@/scr/components/button';
import { useAuth } from '@/scr/context/authContext';

const ConsentScreen = () => {
  const router = useRouter();
  const { theme } = useTheme();
  const { signOut } = useAuth();
  // Use the onboarding store for consent management
  const { consent, updateConsent, setConsentComplete, isConsentValid } = useConsentStore();
  const setCurrentStep = useOnboardingStore((state) => state.setCurrentStep);

  // Animation values
  const iconOpacity = useSharedValue(0);
  const titleOpacity = useSharedValue(0);
  const descriptionOpacity = useSharedValue(0);
  const consentCardsOpacity = useSharedValue(0);
  const buttonOpacity = useSharedValue(0);

  useEffect(() => {
    // Staggered animations
    iconOpacity.value = withDelay(
      100,
      withTiming(1, { duration: 500, easing: Easing.out(Easing.quad) }),
    );
    titleOpacity.value = withDelay(
      200,
      withTiming(1, { duration: 500, easing: Easing.out(Easing.quad) }),
    );
    descriptionOpacity.value = withDelay(
      300,
      withTiming(1, { duration: 500, easing: Easing.out(Easing.quad) }),
    );
    consentCardsOpacity.value = withDelay(
      400,
      withTiming(1, { duration: 500, easing: Easing.out(Easing.quad) }),
    );
    buttonOpacity.value = withDelay(
      500,
      withTiming(1, { duration: 500, easing: Easing.out(Easing.quad) }),
    );
  }, []);

  // Animated styles
  const createAnimatedStyle = (opacity: Animated.SharedValue<number>) =>
    useAnimatedStyle(() => ({
      opacity: opacity.value,
      transform: [{ translateY: (1 - opacity.value) * 20 }],
    }));

  const iconAnimatedStyle = createAnimatedStyle(iconOpacity);
  const titleAnimatedStyle = createAnimatedStyle(titleOpacity);
  const descriptionAnimatedStyle = createAnimatedStyle(descriptionOpacity);
  const consentCardsAnimatedStyle = createAnimatedStyle(consentCardsOpacity);
  const buttonAnimatedStyle = createAnimatedStyle(buttonOpacity);

  // Set current step when screen comes into focus (including back navigation)
  useFocusEffect(
    React.useCallback(() => {
      setCurrentStep(0); // Consent is step 0
    }, [setCurrentStep])
  );

  // Handle privacy policy link press
  const handlePrivacyPolicyPress = async () => {
    try {
      await WebBrowser.openBrowserAsync('https://chronicare.io/privacy-policy', {
        presentationStyle: WebBrowser.WebBrowserPresentationStyle.PAGE_SHEET,
        controlsColor: theme.colors.Primary.primary500,
      });
    } catch (error) {
      console.error('Error opening privacy policy:', error);
    }
  };

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    content: {
      flex: 1,
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingBottom: 30,
    },
    mainContent: {
      flex: 1,
      justifyContent: 'flex-end',
      alignItems: 'stretch',
    },

    scrollContent: {
      flexGrow: 1,
      justifyContent: 'center',
      gap: theme.spacing.spacing.s2,
    },

    title: {
      ...theme.textVariants.heading('lg', 'bold'),
      textAlign: 'left',
      marginBottom: theme.spacing.spacing.s2,
    },
    descriptionContainer: {
      gap: 0,
    },
    descriptionTitle: {
      ...theme.textVariants.text('md', 'semibold'),
      color: theme.colors.Text.text900,
      //lineHeight: 21,
    },
    descriptionText: {
      ...theme.textVariants.text('md', 'regular'),
      color: theme.colors.Text.text900,
      //lineHeight: 21,
    },

    privacyLink: {
      ...theme.textVariants.text('sm', 'regular'),
      color: theme.colors.Primary.primary500,
    },
    privacyLinkPrefix: {
      ...theme.textVariants.text('sm', 'regular'),
      color: theme.colors.Text.text900,
    },
    privacyLinkContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingBottom: theme.spacing.spacing.s2,
    },
    consentCardsContainer: {
      gap: theme.spacing.spacing.s4,
    },
  }), [theme]);

  // Handle agreement changes for individual cards using the store
  const handleCardAgreement = (cardKey: keyof typeof consent) => (isAgreed: boolean) => {
    updateConsent({
      [cardKey]: isAgreed
    });
  };

  // Use store validation for proceed logic
  const canProceed = isConsentValid();
  
  const handleNextPress = () => { 
    // Mark consent as complete in the store
    setConsentComplete();
    router.push('/(auth)/(onboarding)/name');
  };

  const handleSignOut = async () => {
    await signOut();
    router.replace('/(public)/intro');
  };

  return (
    <ImageBackgroundComponent style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.content}>
          <View style={styles.mainContent}>
            <View style={styles.scrollContent}>
              <Animated.View style={iconAnimatedStyle}>
                <FileCheck size={32} color={theme.colors.Text.text900} />
              </Animated.View>

              <Animated.View style={titleAnimatedStyle}>
                <Text style={styles.title}>
                  We believe in healthy relationships
                </Text>
              </Animated.View>

              <Animated.View style={descriptionAnimatedStyle}>
                <View style={styles.descriptionContainer}>
                  <Text style={styles.descriptionText}>
                    <Text style={styles.descriptionTitle}>
                      Your data makes a difference
                    </Text>
                    {'\n'}Anonymized insights based on your data enable our
                    research and industry partners to improve care for chronic
                    diseases.
                    {'\n\n'}
                    <Text style={styles.descriptionTitle}>
                      Committed to privacy
                    </Text>
                    {'\n'}We ensure strict data security and privacy. We commit
                    to transparency every step of the way.
                  </Text>
                </View>

                <View style={styles.privacyLinkContainer}>
                  <Text style={styles.privacyLinkPrefix}>Read our </Text>
                  <TouchableOpacity onPress={handlePrivacyPolicyPress}>
                    <Text style={styles.privacyLink}>Data Privacy Policy</Text>
                  </TouchableOpacity>
                </View>
              </Animated.View>

              <Animated.View style={[styles.consentCardsContainer, consentCardsAnimatedStyle]}>
                <ConsentSection
                  title="Data privacy"
                  items={[
                    {
                      key: 'dataPrivacy',
                      text: 'I consent to the collection and processing of my health data for research purposes',
                      isChecked: consent.dataPrivacy,
                      onAgreeChange: handleCardAgreement('dataPrivacy'),
                    },
                    {
                      key: 'dataSharing',
                      text: 'I consent to the sharing of my anonymized health data with our research partners',
                      isChecked: consent.dataSharing,
                      onAgreeChange: handleCardAgreement('dataSharing'),
                    },
                  ]}
                />
                
                <ConsentSection
                  title="Communication"
                  items={[
                    {
                      key: 'marketing',
                      text: '(Optional) I agree to receive occasional emails about product updates and new features',
                      isChecked: consent.marketing,
                      onAgreeChange: handleCardAgreement('marketing'),
                    },
                  ]}
                />
              </Animated.View>
            </View>

            <Animated.View style={buttonAnimatedStyle}>
              <Button
                variant="iconNeutral"
                title="Continue"
                onPress={handleNextPress}
                disabled={!canProceed}
                icon={<ArrowRight size={18} color={theme.colors.Text.text0} />}
                iconPosition="right"
              />
            </Animated.View>
          </View>
        </View>
      </SafeAreaView>
    </ImageBackgroundComponent>
  );
};

export default ConsentScreen;