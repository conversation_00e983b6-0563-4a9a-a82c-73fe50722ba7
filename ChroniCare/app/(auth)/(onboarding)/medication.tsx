import { View, StyleSheet, SafeAreaView, ScrollView, Text, TouchableOpacity, Alert } from 'react-native';
import React, { useMemo, useState, useRef, useCallback, useEffect } from 'react';
import { Pill, Check, Plus, X } from 'lucide-react-native';
import { useTheme } from '@/scr/context/themeContext';
import IconTitleInstructions from '@/scr/components/IconTitleInstructions';
import { BackNextButton } from '@/scr/components/backNextButton';
import { useUserTypesStore, useMedicationsStore, useOnboardingStore } from '@/scr/utils/onboardingStore';
import { router, useFocusEffect } from 'expo-router';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { ImageBackgroundComponent } from '@/scr/components/onboarding/imageBackground';
import Label from '@/scr/components/forms/Label';
import { BottomSheetModalProvider, BottomSheetModal, BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import ComboBox from '@/scr/components/forms/ComboBox';
import { medications, medicationList } from '@/scr/data/medications';
import { useCompleteOnboarding } from '@/scr/hooks/completeOnboarding';

const Medication = () => {
  const { theme } = useTheme();
  const { getDiagnosedDiseases } = useUserTypesStore();
  const { 
    getMedicationsForDisease, 
    addMedicationToDisease, 
    removeMedicationFromDisease,
    createMedicationEntry,
    setMedicationsComplete 
  } = useMedicationsStore();
  const { completeOnboarding, loading: isOnboardingCompleting } = useCompleteOnboarding();
  const setCurrentStep = useOnboardingStore((state) => state.setCurrentStep);

  // Set current step when screen comes into focus (including back navigation)
  useFocusEffect(
    React.useCallback(() => {
      setCurrentStep(6); // Medication is step 6
    }, [setCurrentStep])
  );
  
  const [currentDiseaseId, setCurrentDiseaseId] = useState<string | number | null>(null);
  const [currentDiseaseName, setCurrentDiseaseName] = useState<string>('');
  const [shouldAutoFocus, setShouldAutoFocus] = useState(false);

  // Bottom sheet ref and snap points
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['85%'], []);

  // Backdrop render function
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    []
  );

  // Handle modal animation to trigger auto-focus
  const handleModalAnimate = useCallback((fromIndex: number, toIndex: number) => {
    if (toIndex === 0) { // Modal is opening
      setShouldAutoFocus(true);
    }
  }, []);

  // Get diseases where user is a diagnosed patient
  const diagnosedDiseases = useMemo(() => {
    return getDiagnosedDiseases();
  }, [getDiagnosedDiseases]);

  // Get current medications for the selected disease
  const getCurrentMedicationsForDisease = useCallback((diseaseId: string | number) => {
    const medicationEntries = getMedicationsForDisease(diseaseId);
    return medicationEntries.map(entry => ({
      id: String(entry.medication.id),
      label: entry.medication.label,
    }));
  }, [getMedicationsForDisease]);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoidingContainer: {
      flex: 1,
      paddingBottom: 100,
    },
    content: {
      flex: 1,
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingBottom: 30,
    },
    mainContent: {
      flex: 1,
      justifyContent: 'flex-end',
      alignItems: 'stretch',
    },
    centeredPageContentWrapper: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'stretch',
      gap: theme.spacing.spacing.s4,
    },
    diseasesScrollContainer: {
      maxHeight: 400,
    },
    diseaseItem: {
      marginBottom: theme.spacing.spacing.s4,
    },
    diseaseName: {
      ...theme.textVariants.text('md', 'regular'),
      color: theme.colors.Text.text900,
      marginBottom: theme.spacing.spacing.s2,
    },
    diseaseContentRow: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      alignItems: 'flex-start',
      gap: theme.spacing.spacing.s2,
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.Background.background200,
      marginVertical: theme.spacing.spacing.s4,
    },
    emptyStateText: {
      ...theme.textVariants.text('md', 'regular'),
      color: theme.colors.Text.text600,
      textAlign: 'center',
      marginTop: theme.spacing.spacing.s4,
    },
    // Bottom Sheet Styles
    bottomSheetContainer: {
      backgroundColor: theme.colors.Background.background0,
      paddingVertical: theme.spacing.spacing.s4,
      paddingHorizontal: theme.spacing.spacing.s4,
      flex: 1,
    },
    bottomSheetTopRow: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
      alignItems: 'center',
      marginBottom: theme.spacing.spacing.s3,
    },
    bottomSheetTitle: {
      ...theme.textVariants.text('md', 'regular'),
      color: theme.colors.Text.text900,
      marginBottom: theme.spacing.spacing.s4,
    },
    closeButton: {
      backgroundColor: theme.colors.Background.background100,
      paddingHorizontal: theme.spacing.spacing.s3,
      paddingVertical: theme.spacing.spacing.s2,
      borderRadius: theme.spacing.borderRadius.lg,
    },
    closeButtonText: {
      ...theme.textVariants.text('sm', 'medium'),
      color: theme.colors.Text.text900,
    },
    comboBoxContainer: {
      flex: 1,
    },
  }), [theme]);

  const handleBack = () => {
    router.back();
  };

  const handleNext = async () => {
    // Mark medications as complete in the store
    setMedicationsComplete();
    console.log('Medication selection stored, attempting to complete onboarding...');

    const success = await completeOnboarding();

    if (success) {
      console.log('Onboarding successfully completed on backend.');
      // Complete onboarding and navigate to main app
      router.replace('/');
    } else {
      console.error('Failed to complete onboarding on the backend.');
      // TODO: Show an alert to the user
      Alert.alert('Error', 'Failed to complete onboarding on the backend.');
    }
  };

  const handleAddMedication = (diseaseId: string | number, diseaseName: string) => {
    setCurrentDiseaseId(diseaseId);
    setCurrentDiseaseName(diseaseName);
    setShouldAutoFocus(false); // Reset auto-focus state
    bottomSheetModalRef.current?.present();
  };

  const handleMedicationSelectionChange = (selectedMedications: medicationList[]) => {
    if (currentDiseaseId === null) return;

    // Get current medications for this disease
    const currentMedicationEntries = getMedicationsForDisease(currentDiseaseId);
    const currentMedicationIds = new Set(currentMedicationEntries.map(entry => entry.medication.id));
    
    // Find newly selected medications (not already in store)
    const newMedications = selectedMedications.filter(med => !currentMedicationIds.has(med.id));
    
    // Find medications to remove (in store but not in selection)
    const selectedMedicationIds = new Set(selectedMedications.map(med => med.id));
    const medicationsToRemove = currentMedicationEntries.filter(entry => !selectedMedicationIds.has(String(entry.medication.id)));

    // Add new medications to store
    newMedications.forEach(medication => {
      const medicationEntry = createMedicationEntry({
        id: medication.id,
        label: medication.label,
      });
      addMedicationToDisease(String(currentDiseaseId), medicationEntry);
    });

    // Remove medications no longer selected
    medicationsToRemove.forEach(entry => {
      removeMedicationFromDisease(String(currentDiseaseId), String(entry.medication.id));
    });
  };

  const handleRemoveMedication = (diseaseId: string | number, medicationId: string | number) => {
    removeMedicationFromDisease(String(diseaseId), String(medicationId));
  };

  const handleCloseBottomSheet = () => {
    setShouldAutoFocus(false); // Reset auto-focus state
    bottomSheetModalRef.current?.dismiss();
  };

  // If no diagnosed diseases, redirect back (shouldn't happen with proper routing)
  if (diagnosedDiseases.length === 0) {
    console.warn('No diagnosed diseases found, redirecting to complete onboarding');
    router.replace('/');
    return null;
  }

  return (
    <BottomSheetModalProvider>
      <ImageBackgroundComponent style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <KeyboardAvoidingView
            behavior="padding"
            style={styles.keyboardAvoidingContainer}
          >
            <View style={styles.content}>
              <View style={styles.mainContent}>
                <View style={styles.centeredPageContentWrapper}>
                  <IconTitleInstructions
                    icon={Pill}
                    title="Your medication."
                    instructions="Select any medications you are currently taking for your diagnosed conditions.
                                  You can always add or modify medications later in your profile."
                    iconSize={32}
                  />
                  
                  <View>
                    <ScrollView 
                      style={styles.diseasesScrollContainer}
                      showsVerticalScrollIndicator={false}
                      contentContainerStyle={{ paddingBottom: theme.spacing.spacing.s2 }}
                    >
                      {diagnosedDiseases.map((disease, index) => {
                        const diseaseId = disease.id;
                        const medicationEntries = getMedicationsForDisease(diseaseId);
                        
                        return (
                          <View key={diseaseId}>
                            <View style={styles.diseaseItem}>
                              <Text style={styles.diseaseName}>
                                {disease.label}
                              </Text>
                              
                              <View style={styles.diseaseContentRow}>
                                <Label
                                  text="Add"
                                  variant="solid"
                                  colorScheme='neutral'
                                  iconLeft={<Plus size={16} color={theme.colors.Text.text0} />}
                                  onPress={() => handleAddMedication(diseaseId, disease.label)}
                                />
                                
                                {/* Display selected medications for this disease */}
                                {medicationEntries.length > 0 && (
                                  <>
                                    {medicationEntries.map((medicationEntry) => (
                                      <Label
                                        key={medicationEntry.medication.id}
                                        text={medicationEntry.medication.label}
                                        variant="solid"
                                        colorScheme="muted"
                                        onRemove={() => handleRemoveMedication(diseaseId, medicationEntry.medication.id)}
                                      />
                                    ))}
                                  </>
                                )}
                              </View>
                            </View>
                            {index < diagnosedDiseases.length - 1 && (
                              <View style={styles.divider} />
                            )}
                          </View>
                        );
                      })}
                   
                    </ScrollView>
                  </View>
                  
                </View>
                
                <BackNextButton
                  onBackPress={handleBack}
                  onNextPress={handleNext}
                  nextTitle="Complete"
                  nextInactive={isOnboardingCompleting} 
                  nextIcon={<Check size={18} color={theme.colors.Text.text0} />}
                  nextBackgroundColor={theme.colors.Primary.primary500}
                />
              </View>
            </View>
          </KeyboardAvoidingView>
        </SafeAreaView>

        {/* Bottom Sheet Modal for Medication Selection */}
        <BottomSheetModal
          ref={bottomSheetModalRef}
          snapPoints={snapPoints}
          enablePanDownToClose={true}
          backgroundStyle={{ backgroundColor: theme.colors.Background.background0 }}
          backdropComponent={renderBackdrop}
          onAnimate={handleModalAnimate}
          handleIndicatorStyle={{
            backgroundColor: theme.colors.Background.background900,
          }}
        >
          <BottomSheetView style={styles.bottomSheetContainer}>
            <View style={styles.bottomSheetTopRow}>
              <TouchableOpacity style={styles.closeButton} onPress={handleCloseBottomSheet} activeOpacity={0.7}>
                <Text style={styles.closeButtonText}>Done</Text>
              </TouchableOpacity>
            </View>
            
            <Text style={styles.bottomSheetTitle}>
              Add medications for {currentDiseaseName}
            </Text>
            
            <View style={styles.comboBoxContainer}>
              <ComboBox<medicationList>
                data={medications}
                onSelectionChange={handleMedicationSelectionChange}
                labelColorScheme="primary"
                placeholder="Search for medications..."
                multiple={true}
                selectedItems={currentDiseaseId ? getCurrentMedicationsForDisease(currentDiseaseId) : []}
                autoFocus={shouldAutoFocus}
              />
            </View>
          </BottomSheetView>
        </BottomSheetModal>
      </ImageBackgroundComponent>
    </BottomSheetModalProvider>
  );
};

export default Medication;
