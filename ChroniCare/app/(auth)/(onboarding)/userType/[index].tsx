import { View, StyleSheet, SafeAreaView, Text, Dimensions, TouchableOpacity, Alert } from 'react-native';
import React, { useMemo, useState, useEffect, useCallback } from 'react';
import { Users, ArrowLeft } from 'lucide-react-native';
import { useTheme } from '@/scr/context/themeContext';
import IconTitleInstructions from '@/scr/components/IconTitleInstructions';
import { BackNextButton } from '@/scr/components/backNextButton';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { useUserTypesStore, useDiseasesStore, useOnboardingStore } from '@/scr/utils/onboardingStore';
import SelectionButton from '@/scr/components/forms/SelectionButton';
import { DatePicker } from '@/scr/components/forms/DatePicker';
import { ImageBackgroundComponent } from '@/scr/components/onboarding/imageBackground';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import { useCompleteOnboarding } from '@/scr/hooks/completeOnboarding';

const SCREEN_WIDTH = Dimensions.get('window').width;
const ANIMATION_DURATION = 300;

const UserType = () => {
  const { theme } = useTheme();
  const { index } = useLocalSearchParams<{ index: string }>();
  
  // Get selected diseases from store
  const { diseases } = useDiseasesStore();
  const { 
    setUserTypeForDisease, 
    getUserTypeForDisease, 
    setUserTypesComplete,
    areAllUserTypesComplete,
    getDiagnosedDiseases,
  } = useUserTypesStore();
  const setOnboardingStep = useOnboardingStore((state) => state.setCurrentStep);
  const { completeOnboarding, loading: isOnboardingCompleting } = useCompleteOnboarding();

  // Set current step when screen comes into focus (including back navigation)
  useFocusEffect(
    React.useCallback(() => {
      setOnboardingStep(5); // UserType is step 5
    }, [setOnboardingStep])
  );

  // Parse index and get current disease
  const currentIndex = parseInt(index || '0', 10);
  const currentDisease = diseases.selectedDiseases[currentIndex];
  
  // Get current user type data for this disease
  const currentUserType = getUserTypeForDisease(currentDisease?.id);
  
  // Local state for this disease's selections
  const [role, setRole] = useState<'patient' | 'caregiver' | undefined>(currentUserType?.role);
  const [diagnosisStatus, setDiagnosisStatus] = useState<'diagnosed' | 'not-diagnosed' | undefined>(currentUserType?.diagnosisStatus);
  const [diagnosisDate, setDiagnosisDate] = useState<Date | undefined>(currentUserType?.diagnosisDate);

  const [currentStep, setCurrentStep] = useState(0); // 0: Role, 1: Diagnosis Status, 2: Diagnosis Date

  // Animation shared values
  const translateXRole = useSharedValue(0);
  const translateXDiagnosisStatus = useSharedValue(SCREEN_WIDTH);
  const translateXDiagnosisDate = useSharedValue(SCREEN_WIDTH);

  const resetAnimationsToStep = useCallback((step: number) => {
    translateXRole.value = step === 0 ? 0 : -SCREEN_WIDTH;
    translateXDiagnosisStatus.value = step === 0 ? SCREEN_WIDTH : (step === 1 ? 0 : -SCREEN_WIDTH);
    translateXDiagnosisDate.value = step === 2 ? 0 : SCREEN_WIDTH;
  }, [translateXRole, translateXDiagnosisStatus, translateXDiagnosisDate]);

  // Update local state when disease changes or on mount
  useEffect(() => {
    const userType = getUserTypeForDisease(currentDisease?.id);
    setRole(userType?.role);
    setDiagnosisStatus(userType?.diagnosisStatus);
    setDiagnosisDate(userType?.diagnosisDate);
    
    // Determine initial step based on existing data for smooth UX if user comes back
    let initialStep = 0;
    if (userType?.role === 'patient') {
      if (userType.diagnosisStatus) {
        initialStep = userType.diagnosisStatus === 'diagnosed' ? 2 : 1;
      } else {
        initialStep = 1;
      }
    }
    setCurrentStep(initialStep);
    resetAnimationsToStep(initialStep);

  }, [currentDisease?.id, getUserTypeForDisease, resetAnimationsToStep]);

  const animatedStyleRole = useAnimatedStyle(() => ({
    transform: [{ translateX: translateXRole.value }],
  }));
  const animatedStyleDiagnosisStatus = useAnimatedStyle(() => ({
    transform: [{ translateX: translateXDiagnosisStatus.value }],
  }));
  const animatedStyleDiagnosisDate = useAnimatedStyle(() => ({
    transform: [{ translateX: translateXDiagnosisDate.value }],
  }));

  const navigateToStep = (nextStep: number) => {
    if (nextStep === 1) { // Role -> Diagnosis Status
      translateXRole.value = withTiming(-SCREEN_WIDTH, { duration: ANIMATION_DURATION });
      translateXDiagnosisStatus.value = withTiming(0, { duration: ANIMATION_DURATION }, () => {
        runOnJS(setCurrentStep)(1);
      });
    } else if (nextStep === 2) { // Diagnosis Status -> Diagnosis Date
      translateXDiagnosisStatus.value = withTiming(-SCREEN_WIDTH, { duration: ANIMATION_DURATION });
      translateXDiagnosisDate.value = withTiming(0, { duration: ANIMATION_DURATION }, () => {
        runOnJS(setCurrentStep)(2);
      });
    }
  };

  const navigateBackToStep = (prevStep: number) => {
    if (prevStep === 0) { // Diagnosis Status -> Role
      translateXDiagnosisStatus.value = withTiming(SCREEN_WIDTH, { duration: ANIMATION_DURATION });
      translateXRole.value = withTiming(0, { duration: ANIMATION_DURATION }, () => {
        runOnJS(setCurrentStep)(0);
        // Optionally clear diagnosisStatus and diagnosisDate when going back to role
        // runOnJS(setDiagnosisStatus)(undefined);
        // runOnJS(setDiagnosisDate)(undefined);
      });
    } else if (prevStep === 1) { // Diagnosis Date -> Diagnosis Status
      translateXDiagnosisDate.value = withTiming(SCREEN_WIDTH, { duration: ANIMATION_DURATION });
      translateXDiagnosisStatus.value = withTiming(0, { duration: ANIMATION_DURATION }, () => {
        runOnJS(setCurrentStep)(1);
        // Optionally clear diagnosisDate
        // runOnJS(setDiagnosisDate)(undefined);
      });
    }
  };
  
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoidingContainer: {
      flex: 1,
      paddingBottom: 100,
    },
    content: {
      flex: 1,
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingBottom: 30,
    },
    mainContent: {
      flex: 1,
      justifyContent: 'flex-end',
      alignItems: 'stretch',
    },
    centeredContent: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'stretch',
    },
    pageWrapper: {
      width: '100%',
      alignItems: 'stretch',
      gap: theme.spacing.spacing.s3,
    },
    questionSlideHost: {
      position: 'relative',
      overflow: 'hidden',
      width: '100%',
      height: 200,
    },
    animatedSection: { 
      width: '100%',
      height: '100%',
      position: 'absolute',
      top: 0,
      left: 0,
      justifyContent: 'center',
      alignItems: 'center',
    },
    sectionContainer: {
      gap: theme.spacing.spacing.s1,
      width: '100%',
    },
    sectionTitle: {
      ...theme.textVariants.text('sm', 'semibold'),
      color: theme.colors.Text.text950,
      paddingBottom: theme.spacing.spacing.s2,
    },
    selectionContainer: {
      gap: theme.spacing.spacing.s3,
    },
  }), [theme]);

  const handleRoleSelection = (selectedRole: 'patient' | 'caregiver') => {
    setRole(selectedRole);
    if (selectedRole === 'caregiver') {
      setDiagnosisStatus(undefined);
      setDiagnosisDate(undefined);
      setUserTypeForDisease(currentDisease.id, selectedRole);
      // Ensure other steps are reset if user toggles
      if (currentStep !== 0) {
        resetAnimationsToStep(0);
        setCurrentStep(0);
      }
    } else { // patient
      setDiagnosisStatus(undefined); // Clear previous status before navigating
      setDiagnosisDate(undefined);
      navigateToStep(1);
    }
  };

  const handleDiagnosisStatusSelection = (status: 'diagnosed' | 'not-diagnosed') => {
    setDiagnosisStatus(status);
    if (status === 'not-diagnosed') {
      setDiagnosisDate(undefined);
      setUserTypeForDisease(currentDisease.id, 'patient', status);
      // Ensure date step is reset if user toggles
      if (currentStep === 2) {
         resetAnimationsToStep(1); // Go back to step 1 view if they were on step 2
         setCurrentStep(1);
      }
    } else { // diagnosed
      // We save without date here, date is optional and saved on change
      setUserTypeForDisease(currentDisease.id, 'patient', status, diagnosisDate);
      navigateToStep(2);
    }
  };

  const handleDiagnosisDateChange = (date: Date | undefined) => {
    setDiagnosisDate(date);
    if (role === 'patient' && diagnosisStatus) {
      setUserTypeForDisease(currentDisease.id, 'patient', diagnosisStatus, date);
    }
  };

  const isPageSelectionComplete = () => { // Renamed to avoid conflict
    if (!role) return false;
    if (role === 'caregiver') return true;
    if (role === 'patient') {
      return diagnosisStatus !== undefined;
    }
    return false;
  };

  const handleMainBack = () => {
    if (isOnboardingCompleting) return;
    if (currentStep === 2) {
      navigateBackToStep(1);
    } else if (currentStep === 1) {
      navigateBackToStep(0);
    } else {
      router.back();
    }
  };

  const handleNext = async () => {
    const isLastDisease = currentIndex >= diseases.selectedDiseases.length - 1;
    
    if (isLastDisease) {
      if (areAllUserTypesComplete()) {
        setUserTypesComplete();
      }
      
      // Check if there are any diagnosed diseases. If so, go to medication screen.
      // Note: getDiagnosedDiseases() gets the latest from the store.
      const diagnosedDiseases = getDiagnosedDiseases();

      if (diagnosedDiseases.length > 0) {
        router.push('/(auth)/(onboarding)/medication');
      } else {
        // If no diagnosed diseases, complete onboarding here.
        const success = await completeOnboarding();
        if (success) {
          router.replace('/');
        } else {
          Alert.alert('Error', 'Failed to complete onboarding. Please try again.');
        }
      }
    } else {
      router.push({
        pathname: '/(auth)/(onboarding)/userType/[index]',
        params: { index: currentIndex + 1 },
      });
    }
  };

  // If no disease is found, navigate back
  if (!currentDisease) {
    router.back();
    return null;
  }

  const maxDate = new Date(); // Today
  const minDate = new Date();
  minDate.setFullYear(1900); // Reasonable minimum date

  return (
    <BottomSheetModalProvider>
      <ImageBackgroundComponent style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <KeyboardAvoidingView
            behavior="padding"
            style={styles.keyboardAvoidingContainer}
          >
            <View style={styles.content}>
              <View style={styles.mainContent}>
                <View style={styles.centeredContent}>
                  <View style={styles.pageWrapper}>
                    <IconTitleInstructions
                      icon={Users}
                      title={ (
                        <Text style={theme.textVariants.heading('lg', 'bold')}>
                          Your relationship with <Text style={{ color: theme.colors.Primary.primary500 }}>{currentDisease.label}</Text>
                        </Text>
                      )}
                    />
                    
                    <View style={styles.questionSlideHost}>
                      {/* Role Selection */}
                      <Animated.View style={[styles.animatedSection, animatedStyleRole]}>
                        <View style={styles.sectionContainer}>
                          <Text style={styles.sectionTitle}>What describes you best?</Text>
                          <View style={styles.selectionContainer}>
                            <SelectionButton
                              isSelected={role === 'patient'}
                              onPress={() => handleRoleSelection('patient')}
                              label="I am a patient"
                              multiSelect={false}
                            />
                            <SelectionButton
                              isSelected={role === 'caregiver'}
                              onPress={() => handleRoleSelection('caregiver')}
                              label="I am a caregiver"
                              multiSelect={false}
                            />
                          </View>
                        </View>
                      </Animated.View>

                      {/* Diagnosis Status Selection */}
                      <Animated.View style={[styles.animatedSection, animatedStyleDiagnosisStatus]}>
                        <View style={styles.sectionContainer}>
                          <Text style={styles.sectionTitle}>
                            Have you received a diagnosis from a medical professional?
                          </Text>
                          <View style={styles.selectionContainer}>
                            <SelectionButton
                              isSelected={diagnosisStatus === 'diagnosed'}
                              onPress={() => handleDiagnosisStatusSelection('diagnosed')}
                              label="Yes"
                              multiSelect={false}
                            />
                            <SelectionButton
                              isSelected={diagnosisStatus === 'not-diagnosed'}
                              onPress={() => handleDiagnosisStatusSelection('not-diagnosed')}
                              label="No"
                              multiSelect={false}
                            />
                          </View>
                        </View>
                      </Animated.View>

                      {/* Diagnosis Date */}
                      <Animated.View style={[styles.animatedSection, animatedStyleDiagnosisDate]}>
                        <View style={styles.sectionContainer}>
                          <DatePicker
                            label="When did you receive the diagnosis? Only year and month is enough (optional)"
                            placeholder="Press to select date"
                            value={diagnosisDate}
                            onDateChange={handleDiagnosisDateChange}
                            required={false}
                            maximumDate={maxDate}
                            minimumDate={minDate}
                          />
                        </View>
                      </Animated.View>
                    </View>
                  </View>
                </View>
                
                <BackNextButton
                  onBackPress={handleMainBack}
                  onNextPress={handleNext}
                  nextTitle={currentIndex >= diseases.selectedDiseases.length - 1 ? "Continue" : "Next"}
                  nextInactive={!isPageSelectionComplete() || isOnboardingCompleting}
                />
              </View>
            </View>
          </KeyboardAvoidingView>
        </SafeAreaView>
      </ImageBackgroundComponent>
    </BottomSheetModalProvider>
  );
};

export default UserType;
