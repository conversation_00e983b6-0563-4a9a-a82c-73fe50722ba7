import React from 'react'
import { Stack } from 'expo-router'
import { View, StyleSheet,SafeAreaView, Platform, StatusBar } from 'react-native'
import { useTheme } from '../../../scr/context/themeContext'
import { useOnboardingProgress } from '../../../scr/utils/onboardingStore'
import ProgressBar from '../../../scr/components/onboarding/ProgressBar'


const OnboardingLayout = () => {
  const { theme } = useTheme()
  const { getCompletionPercentage } = useOnboardingProgress()
  
  const completionPercentage = getCompletionPercentage()

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.Background.background0,
    },
    progressContainer: {
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingTop: theme.spacing.spacing.s4,
      paddingBottom: theme.spacing.spacing.s3,
      alignItems: 'flex-start',
      ...(Platform.OS === 'android' && { paddingTop: (StatusBar.currentHeight ?? 0) + theme.spacing.spacing.s4 }),
    },
    contentContainer: {
      flex: 1,
    },
  })

  return (
    <View style={styles.container}>
      {/* Progress Bar - Always visible at top left */}
      <SafeAreaView>
      <View style={styles.progressContainer}>
        <ProgressBar 
          progress={completionPercentage}
          height={6}
        />
      </View>
      </SafeAreaView>
      
      {/* Screen Content */}
      <View style={styles.contentContainer}>
        <Stack
          screenOptions={{
            headerShown: false,
            contentStyle: { 
              backgroundColor: 'transparent',
              flex: 1,
            },
            animation: 'slide_from_right',
          }}
        >
        <Stack.Screen 
          name="consent" 
          options={{
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="info" 
          options={{
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="profilePicture" 
          options={{
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="disease" 
          options={{
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="userType" 
          options={{
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="medication" 
          options={{
            headerShown: false,
          }} 
        />
        </Stack>
      </View>
    </View>
  )
}

export default OnboardingLayout;