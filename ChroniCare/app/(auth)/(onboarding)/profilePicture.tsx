import { View, Text, SafeAreaView, StyleSheet, TouchableOpacity, Image } from 'react-native'
import React, { useMemo, useCallback, useRef, useEffect } from 'react'
import IconTitleInstructions from '@/scr/components/IconTitleInstructions'
import { Camera, UserRound, Edit, ImageUp, Trash2 } from 'lucide-react-native'
import { useTheme } from '@/scr/context/themeContext'
import { BackNextButton } from '@/scr/components/backNextButton'
import { Button } from '@/scr/components/button'
import { router, useFocusEffect } from 'expo-router'
import BottomSheet, { BottomSheetView, BottomSheetModalProvider, BottomSheetBackdrop } from '@gorhom/bottom-sheet'
import * as ImagePicker from 'expo-image-picker'
import { KeyboardAvoidingView } from 'react-native-keyboard-controller'
import { useOnboardingStore } from '@/scr/utils/onboardingStore'
import { ImageBackgroundComponent } from '@/scr/components/onboarding/imageBackground';
import { useProfilePicture } from '@/scr/hooks/useProfilePicture'

const profilePicture = () => {
    const { theme } = useTheme();
    const bottomSheetRef = useRef<BottomSheet>(null);
    
    // Use the onboarding store for profile picture management
    const { 
        profilePicture: profilePictureData, 
        setProfilePicture, 
        setProfilePictureComplete,
        setCurrentStep
    } = useOnboardingStore();

    // Use the profile picture hook to get existing photo and upload functionality
    const { 
        existingPhotoURL, 
        uploadProfilePicture,
        removeProfilePicture,
        isUploading,
        isRemoving
    } = useProfilePicture({
        onboardingStoreApi: { setProfilePicture }
    });
    
    // Set current step when screen comes into focus (including back navigation)
    useFocusEffect(
        React.useCallback(() => {
            setCurrentStep(3); // ProfilePicture is step 3
        }, [setCurrentStep])
    );

    // Bottom sheet snap points
    const snapPoints = useMemo(() => ['35%'], []);

    // Bottom sheet callbacks
    const handleSheetChanges = useCallback((index: number) => {
        // console.log('handleSheetChanges', index);
    }, []);

    const handlePresentModalPress = useCallback(() => {
        bottomSheetRef.current?.expand();
    }, []);

    const handleClosePress = useCallback(() => {
        bottomSheetRef.current?.close();
    }, []);

    // Profile picture actions using the store and upload functionality
    const handleUploadPicture = useCallback(async () => {
        try {
            // Request permission to access media library
            const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
            if (status !== 'granted') {
                alert('Sorry, we need camera roll permissions to make this work!');
                return;
            }

            // Launch image library
            let result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ['images'],
                allowsEditing: true,
                aspect: [1, 1],
                quality: 0.5, // Reduce quality for faster upload
            });

            if (!result.canceled && result.assets && result.assets.length > 0) {
                const imageUri = result.assets[0].uri;
                const mimeType = result.assets[0].mimeType;

                if (!mimeType) {
                    alert('Could not determine file type. Please select another image.');
                    return;
                }
                
                // Set temporary local image for immediate UI feedback
                // The hook will handle the zustand update now.
                // setProfilePicture(imageUri); 
                
                // Upload to cloud storage
                await uploadProfilePicture(imageUri, mimeType);
            }
            handleClosePress();
        } catch (error) {
            console.error('Error uploading picture:', error);
            alert('Failed to upload picture. Please try again.');
        }
        
    }, [uploadProfilePicture, handleClosePress]);


    const handleRemovePicture = useCallback(async () => {
        try {
            // Remove profile picture from backend and local state
            await removeProfilePicture();
            handleClosePress();
        } catch (error) {
            console.error('Error removing picture:', error);
            alert('Failed to remove picture. Please try again.');
        }
    }, [removeProfilePicture]);

    const handleNext = useCallback(() => {
        // Mark profile picture as complete in the store
        setProfilePictureComplete();
        router.push('/(auth)/(onboarding)/disease');
    }, [setProfilePictureComplete]);

    // renderBackdrop component
    const renderBackdrop = useCallback(
        (props: any) => (
            <BottomSheetBackdrop
                {...props}
                appearsOnIndex={0} // Show backdrop when sheet is opening (index 0 or 1)
                disappearsOnIndex={-1} // Hide backdrop when sheet is closed (index -1)
                pressBehavior="close" // Close sheet on backdrop press
            />
        ),
        []
    );

    const styles = useMemo(() => StyleSheet.create({
        container: {
            flex: 1,
        },
        safeArea: {
            flex: 1,
        },
        keyboardAvoidingContainer: {
            flex: 1,
            paddingBottom: 100,
        },
        content: {
            flex: 1,
            paddingHorizontal: theme.spacing.spacing.s4,
            paddingBottom: 30,
        },
        mainContent: {
            flex: 1,
            justifyContent: 'flex-end',
            alignItems: 'stretch',
        },
        centeredPageContentWrapper: {
            flex: 1,
            justifyContent: 'center',
            alignItems: 'stretch',
        },
        pageContentContainer: {
            gap: theme.spacing.spacing.s4,
            alignItems: 'flex-start',
        },
        headerSection: {
            alignItems: 'flex-start',
            width: '100%',
        },
        imageSection: {
            alignItems: 'flex-start',
            paddingVertical: theme.spacing.spacing.s4,
            width: '100%',
        },
        imageContainer: {
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            gap: theme.spacing.spacing.s4,
            width: '100%',
        },
        imageCircle: {
            width: 96,
            height: 96,
            borderRadius: 60,
            backgroundColor: theme.colors.Background.background900,
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
        },
        profileImageStyle: {
            width: '100%',
            height: '100%',
        },
        editButton: {
            minWidth: 80,
        },
        bottomSheetContent: {
            
            flex: 1,
            paddingHorizontal: theme.spacing.spacing.s6,
            paddingVertical: theme.spacing.spacing.s4,
        },
        bottomSheetTitle: {
            ...theme.textVariants.heading('lg', 'semibold'),
            color: theme.colors.Text.text950,
            marginBottom: theme.spacing.spacing.s5,
            textAlign: 'center',
        },
        buttonContainer: {
            gap: theme.spacing.spacing.s3,
        },
    }), [theme]);

    return (
        <BottomSheetModalProvider>
            <ImageBackgroundComponent style={styles.container}>
                <SafeAreaView style={styles.safeArea}>
                    <KeyboardAvoidingView
                        behavior="padding"
                        style={styles.keyboardAvoidingContainer}
                    >
                        <View style={styles.content}>
                            <View style={styles.mainContent}>
                                <View style={styles.centeredPageContentWrapper}>
                                    <View style={styles.pageContentContainer}>
                                        <View style={styles.headerSection}>
                                            <IconTitleInstructions
                                                icon={Camera}
                                                title="Add your profile picture"
                                                instructions="Add a picture of yourself as profile picture (optional). You always have the option to post or reply anonymously in the community."
                                            />
                                        </View>

                                        <View style={styles.imageSection}>
                                            <View style={styles.imageContainer}>
                                                <TouchableOpacity 
                                                    style={styles.imageCircle}
                                                    onPress={handlePresentModalPress}
                                                    activeOpacity={0.7}
                                                >
                                                    {(profilePictureData.imageUri || existingPhotoURL) ? (
                                                        <Image 
                                                            source={{ uri: profilePictureData.imageUri || existingPhotoURL || '' }} 
                                                            style={styles.profileImageStyle} 
                                                        />
                                                    ) : (
                                                        <UserRound 
                                                            size={48} 
                                                            color={theme.colors.Text.text0} 
                                                            strokeWidth={3}
                                                        />
                                                    )}
                                                </TouchableOpacity>

                                                <Button
                                                    title="Edit"
                                                    variant="iconNeutral"
                                                    size="small"
                                                    icon={<Edit size={18} color={theme.colors.Text.text0} />}
                                                    iconPosition="left"
                                                    onPress={handlePresentModalPress}
                                                    style={styles.editButton}
                                                />
                                            </View>
                                        </View>
                                    </View>
                                </View>

                                <BackNextButton
                                    onBackPress={() => router.back()}
                                    onNextPress={handleNext}
                                    nextTitle={(profilePictureData.imageUri || existingPhotoURL) ? "Next" : "Skip"}
                                />
                            </View>
                        </View>
                    </KeyboardAvoidingView>

                    <BottomSheet
                        ref={bottomSheetRef}
                        index={-1}
                        snapPoints={snapPoints}
                        onChange={handleSheetChanges}
                        enablePanDownToClose={true}
                        backgroundStyle={{ backgroundColor: theme.colors.Background.background0 }}
                        handleIndicatorStyle={{ backgroundColor: theme.colors.Text.text300 }}
                        backdropComponent={renderBackdrop}
                    >
                        <BottomSheetView style={styles.bottomSheetContent}>
                            <Text style={styles.bottomSheetTitle}>Choose an option</Text>
                            
                            <View style={styles.buttonContainer}>
                                <Button
                                    title={isUploading ? "Uploading..." : "Upload picture"}
                                    variant="iconNeutral"
                                    icon={<ImageUp size={18} color={theme.colors.Text.text0} />}
                                    size="medium"
                                    onPress={handleUploadPicture}
                                    disabled={isUploading || isRemoving}
                                />
                                
                                {/* <Button
                                    title="Take picture"
                                    variant="iconNeutral"
                                    icon={<Camera size={18} color={theme.colors.Text.text0} />}
                                    size="medium"
                                    onPress={handleTakePicture}
                                /> */}
                                
                                <Button
                                    title={isRemoving ? "Removing..." : "Remove picture"}
                                    variant="iconOutline"
                                    icon={<Trash2 size={18} color={theme.colors.Text.text950} />}
                                    size="medium"
                                    onPress={handleRemovePicture}
                                    textColor={theme.colors.Text.text950}
                                    disabled={(!profilePictureData.imageUri && !existingPhotoURL) || isRemoving || isUploading}
                                />
                            </View>
                        </BottomSheetView>
                    </BottomSheet>
                </SafeAreaView>
            </ImageBackgroundComponent>
        </BottomSheetModalProvider>
    )
}

export default profilePicture