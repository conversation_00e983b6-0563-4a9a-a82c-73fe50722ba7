import { router, Stack } from 'expo-router';
import { useTheme } from '../../scr/context/themeContext';
import { TouchableOpacity } from 'react-native';
import { X } from 'lucide-react-native';

const AuthLayout = () => {
  const theme = useTheme();
  return (
    <Stack
    screenOptions={{
      contentStyle: {
      },
      headerShown: false,
      headerShadowVisible: false,
    }}
    >
      <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
      <Stack.Screen name="(onboarding)" options={{ headerShown: false }} />
      <Stack.Screen name="(modal)/create" options={{ 
        presentation: 'modal',
        title: '',
        headerShown: true,
        headerShadowVisible: false,
        headerStyle: {
          backgroundColor: theme.theme.colors.Background.background0,
        },
        headerRight: () => (
          <TouchableOpacity onPress={() => router.back()}>
            <X size={24} color={theme.theme.colors.Text.text950} />
          </TouchableOpacity>
        )
        }} />
    </Stack>
    
  );
};

export default AuthLayout;
