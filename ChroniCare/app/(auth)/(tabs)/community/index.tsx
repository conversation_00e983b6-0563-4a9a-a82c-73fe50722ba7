import React from "react";
import { View, Text, StyleSheet, FlatList, ActivityIndicator, RefreshControl, ScrollView } from "react-native";
import { useEffect, useMemo, useCallback } from "react";
import { useTheme } from "@/scr/context/themeContext";
import Label from "@/scr/components/forms/Label";
import { useQuery } from "@apollo/client";
import { GET_THREADS } from "@/scr/graphql/queries";
import ThreadCard from "@/scr/components/community/threadCard";
import { useUser } from "@/scr/context/userContext";
import { useNavigation } from "expo-router";
import { useBottomTabBarHeight } from "@react-navigation/bottom-tabs";
import Animated, { runOnJS, useAnimatedScrollHandler, useSharedValue } from "react-native-reanimated";
import { useIsFocused } from "@react-navigation/native";


const CommunityScreen = () => {
  const { theme } = useTheme();
  const { user } = useUser();

  // Animmation
  const navigation = useNavigation();
  const tabbarHeight = useBottomTabBarHeight();
  const isFocused = useIsFocused();
  const lastScrollY = useSharedValue(0);
  const tabBarMarginBottom = useSharedValue(0);
  const scrollUpBuffer = useSharedValue(0);

  const updateTabBarMargin = (margin: number) => {
    navigation.getParent()?.setOptions({
      tabBarStyle: {
        marginBottom: margin,
        display: "flex",
      },
    });
  };

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      if (!isFocused) {
        return;
      }
      const currentOffset = event.contentOffset.y;
      const diff = currentOffset - lastScrollY.value;

      if (currentOffset <= 0) {
        tabBarMarginBottom.value = 0;
        scrollUpBuffer.value = 0;
      } else {
        if (diff > 0) { // Scrolling down
          scrollUpBuffer.value = 0;
          const newMargin = tabBarMarginBottom.value - diff * 2;
          tabBarMarginBottom.value = Math.max(-tabbarHeight, Math.min(0, newMargin));
        } else if (diff < 0) { // Scrolling up
          scrollUpBuffer.value += -diff;
          if (scrollUpBuffer.value > 15) {
            const newMargin = tabBarMarginBottom.value - diff * 2;
            tabBarMarginBottom.value = Math.max(-tabbarHeight, Math.min(0, newMargin));
          }
        }
      }

      lastScrollY.value = currentOffset;
      runOnJS(updateTabBarMargin)(tabBarMarginBottom.value);
    },
  });

  const { data, loading, error, refetch } = useQuery(GET_THREADS, {
    variables: { 
      limit: 10,
      communityId: user?.communities?.[0]?.id || '',
    },
  });

  const threadList = useMemo(() => data?.threads || [], [data?.threads]);

  const handleRefresh = useCallback(async () => {
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing threads:', error);
    }
  }, [refetch]);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: theme.spacing.spacing.s4,
      //paddingTop: theme.spacing.spacing.s1,
      gap: theme.spacing.spacing.s4,
      backgroundColor: theme.colors.Background.background0,
    },
    threadContainer: {
      gap: theme.spacing.spacing.s2,
    },
    labelContainer: {
      flexDirection: 'row',
      gap: theme.spacing.spacing.s2,
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    dividerContainer: {
      paddingBottom: theme.spacing.spacing.s4,
    },
    divider: {
      height: 1,
      backgroundColor: theme.colors.Background.background100,
    }
  }), [theme]); 

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.centered}>
          <ActivityIndicator size="large" />
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.centered}>
          <Text style={{ color: theme.colors.Text.text0 }}>Error: {error.message}</Text>
        </View>
      );
    }
    
    if (!loading && threadList.length === 0) {
      return (
        <ScrollView
          contentContainerStyle={styles.centered}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={handleRefresh}
              tintColor={theme.colors.Background.background900}
              colors={[theme.colors.Background.background900]}
            />
          }
        >
          <Text style={{ color: theme.colors.Text.text900 }}>No posts found in this community.</Text>
        </ScrollView>
      )
    }

    return (
      <Animated.FlatList
        onScroll={scrollHandler}
        scrollEventThrottle={16}
        data={threadList}
        keyExtractor={(item) => item._id}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            onRefresh={handleRefresh}
            tintColor={theme.colors.Background.background900}
            colors={[theme.colors.Background.background900]}
          />
        }
        renderItem={({ item }) => (
          <View style={styles.threadContainer}>
            <ThreadCard threadId={item._id} />
            <View style={styles.dividerContainer}>
              <View style={styles.divider} />
            </View>
          </View>
        )}
      />
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.labelContainer}>
        <Label text={user?.communities?.[0]?.name || ''} variant="solid" colorScheme="primary" />
      </View>
      {renderContent()}
    </View>
  );
};

export default CommunityScreen;