import React from 'react'
import { Stack } from 'expo-router'
import AppHeader from '@/scr/components/global/AppHeader'
import ThreadHeader from '@/scr/components/global/ThreadHeader'

const CommunityLayout = () => {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          header: () => <AppHeader />,
        }} 
      />
      <Stack.Screen 
        name="[threadId]" 
        options={{ 
          header: () => <ThreadHeader />,
        }} 
      />
    </Stack>
  )
}

export default CommunityLayout