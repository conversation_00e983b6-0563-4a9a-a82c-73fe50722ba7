import { View, Text, StyleSheet, FlatList, ActivityIndicator } from 'react-native';
import React, { useMemo } from 'react';
import { Stack, useLocalSearchParams } from 'expo-router';
import { useQuery } from '@apollo/client';
import { GET_COMMENTS } from '@/scr/graphql/queries';
import ThreadCard from '@/scr/components/community/threadCard';
import { useTheme } from '@/scr/context/themeContext';
import CommentCard, { Comment } from '@/scr/components/community/CommentCard';

interface CommentWithNesting extends Comment {
  nestingLevel: number;
}

const ThreadDetailScreen = () => {
  const { threadId } = useLocalSearchParams<{ threadId: string }>();
  const { theme } = useTheme();

  const { data, loading, error } = useQuery(GET_COMMENTS, {
    variables: { threadId },
    skip: !threadId,
  });

  // Organize comments hierarchically
  const organizedComments = useMemo(() => {
    if (!data?.comments) return [];

    const comments: Comment[] = data.comments;
    const commentsByParent = new Map<string, Comment[]>();
    
    // Initialize map with empty arrays for all potential parents
    comments.forEach(comment => {
      if (comment.parentCommentId) {
        if (!commentsByParent.has(comment.parentCommentId)) {
          commentsByParent.set(comment.parentCommentId, []);
        }
      }
    });

    // Group comments by their parent
    comments.forEach(comment => {
      if (comment.parentCommentId) {
        commentsByParent.get(comment.parentCommentId)?.push(comment);
      }
    });

    // Recursive function to flatten the comment tree
    const getCommentsRecursively = (parentId: string | null, nestingLevel: number): CommentWithNesting[] => {
      // For root comments, parentId is null. In other cases it is a string.
      const key = parentId === null ? '' : parentId;
      const directReplies = parentId === null 
        ? comments.filter(c => !c.parentCommentId)
        : (commentsByParent.get(key) || []);

      const result: CommentWithNesting[] = [];

      directReplies.forEach(reply => {
        result.push({ ...reply, nestingLevel });
        result.push(...getCommentsRecursively(reply._id, nestingLevel + 1));
      });

      return result;
    };

    return getCommentsRecursively(null, 0);
  }, [data?.comments]);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.Background.background0,
      paddingHorizontal: theme.spacing.spacing.s4,
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    separator: {
      height: 1,
      backgroundColor: theme.colors.Background.background100,
      marginVertical: theme.spacing.spacing.s4,
    },
  }), [theme]);

  if (!threadId) {
    return (
      <View style={styles.centered}>
        <Text>Thread ID is missing.</Text>
      </View>
    );
  }

  const renderHeader = () => (
    <View>
      <ThreadCard threadId={threadId} isClickable={false} />
      <View style={styles.separator} />
    </View>
  );

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ title: 'Post' }} />
      <FlatList
        data={organizedComments}
        keyExtractor={(item) => item._id}
        ListHeaderComponent={renderHeader}
        renderItem={({ item }) => (
          <CommentCard 
            comment={item} 
            nestingLevel={item.nestingLevel}
          />
        )}
        showsVerticalScrollIndicator={false}
        ListFooterComponent={() => {
          if (loading) return <ActivityIndicator style={{ marginVertical: 20 }} />;
          if (error) return <Text style={{ color: theme.colors.Text.text0, textAlign: 'center', marginVertical: 20 }}>Error loading comments.</Text>;
          if (organizedComments.length === 0 && !loading) {
            return (
              <View style={{ marginTop: 20, alignItems: 'center' }}>
                <Text style={{ ...theme.textVariants.text('lg', 'regular'), color: theme.colors.Text.text500, textAlign: 'center' }}>
                  No comments yet.
                </Text>
                <Text style={{ ...theme.textVariants.text('md', 'regular'), color: theme.colors.Text.text500, textAlign: 'center', marginTop: theme.spacing.spacing.s1 }}>
                  Be the first to share your thoughts!
                </Text>
              </View>
            );
          }
          return null;
        }}
      />
    </View>
  );
};

export default ThreadDetailScreen;
