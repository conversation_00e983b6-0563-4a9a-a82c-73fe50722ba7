import { View, Text, Image } from 'react-native'
import React, { useState } from 'react'
import { Tabs, useRouter } from 'expo-router'
import { useTheme } from '../../../scr/context/themeContext'
import * as Haptics from 'expo-haptics'
import { UsersRound, CirclePlus, HeartPulse } from 'lucide-react-native'
import AppHeader from '@/scr/components/global/AppHeader'


const _layout = () => {
  const theme = useTheme();
  const router = useRouter();   

  return (
    <View style={{ flex: 1, backgroundColor: theme.theme.colors.Background.background0 }}>
    <Tabs
    screenOptions={{
        tabBarShowLabel: false,
        tabBarInactiveTintColor: theme.theme.colors.Text.text900,
        tabBarStyle: {
            backgroundColor: theme.theme.colors.Background.background0,
        },
        headerStyle: {
            backgroundColor: theme.theme.colors.Background.background0,
        },
        headerShadowVisible: false,
        headerTitle: () => null,
    }}
    >
        

        <Tabs.Screen name="community" options={{
            headerShown: false,
            tabBarIcon: ({ color, size, focused }) => (
                <UsersRound color={color} size={size} />
            )
        }} />
        <Tabs.Screen name="create" options={{
        headerShown: false,
        title: 'Create',
        tabBarIcon: ({ color, size, focused }) => (
            <CirclePlus color={color} size={size} />
        )
        }}
        listeners={{
            tabPress: (e) => {
                e.preventDefault();
                Haptics.selectionAsync();
                router.push('/(auth)/(modal)/create');
            }
        }} />
       <Tabs.Screen name="journey" options={{
        header: () => <AppHeader />,
        tabBarIcon: ({ color, size, focused }) => (
            <HeartPulse color={color} size={size} />
        )}} />
      
    </Tabs>
    </View>
    
  )
}

export default _layout