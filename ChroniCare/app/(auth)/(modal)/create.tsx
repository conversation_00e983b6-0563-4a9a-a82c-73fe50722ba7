import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { useTheme } from '../../../scr/context/themeContext';

const create = () => {
    const theme = useTheme();

    const styles = StyleSheet.create({
      container: {
        flex: 1,
        backgroundColor: theme.theme.colors.Background.background0,
        justifyContent: 'center',
        alignItems: 'center',
        padding: theme.theme.spacing.spacing.s5
      },
      text: {
          color: theme.theme.colors.Text.text900,
          fontSize: theme.theme.typography.textSize.md,
          textAlign: 'center',
          fontFamily: theme.theme.typography.fontFamily.inter,
      }
    });

  return (
    <View style={styles.container}>
      <Text style={styles.text}>Her vil du kunne oprette nye opslag i fællesskabet.</Text>
    </View>
  )
}

export default create