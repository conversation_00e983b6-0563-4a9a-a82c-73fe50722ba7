import React, { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { useTheme } from '@/scr/context/themeContext';
import { useOnboarding } from '@/scr/context/onboardingContext';
import { HeartHandshake } from 'lucide-react-native';
import Tooltip from '@/scr/components/onboarding/tooltip';
import { ImageBackgroundComponent } from '@/scr/components/onboarding/imageBackground';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withDelay,
  Easing
} from 'react-native-reanimated';

export default function Intro() {
  const { theme } = useTheme();
  const router = useRouter();
  
  // Animation values
  const titleOpacity = useSharedValue(0);
  const tooltipOpacity = useSharedValue(0);
  const logoOpacity = useSharedValue(0);
  
  useEffect(() => {
    // Start animations with staggered delays
    titleOpacity.value = withDelay(0, withTiming(1, {
      duration: 800,
      easing: Easing.out(Easing.quad),
    }));
    
    tooltipOpacity.value = withDelay(100, withTiming(1, {
      duration: 800,
      easing: Easing.out(Easing.quad),
    }));
    
    logoOpacity.value = withDelay(100, withTiming(1, {
      duration: 800,
      easing: Easing.out(Easing.quad),
    }));
  }, []);

  // Animated styles
  const titleAnimatedStyle = useAnimatedStyle(() => ({
    opacity: titleOpacity.value,
    transform: [{ translateY: (1 - titleOpacity.value) * 20 }],
  }));

  const tooltipAnimatedStyle = useAnimatedStyle(() => ({
    opacity: tooltipOpacity.value,
    transform: [{ translateY: (1 - tooltipOpacity.value) * 20 }],
  }));

  const logoAnimatedStyle = useAnimatedStyle(() => ({
    opacity: logoOpacity.value,
    transform: [{ translateY: (1 - logoOpacity.value) * 20 }],
  }));

  const handleContinue = async () => {
    router.replace('/(public)/getStarted');
  };

  return (
    <ImageBackgroundComponent style={styles.container}>
      <SafeAreaView style={[styles.container, { backgroundColor: 'transparent' }]}>
        <View style={styles.content}>
          <View style={styles.centerContainer}>
            <Animated.View style={titleAnimatedStyle}>
              <Text style={[theme.textVariants.heading('xl3', 'bold'), styles.title]}>
                <Text style={{ color: theme.colors.Text.text950 }}>Navigate your health journey. </Text>
                <Text style={{ color: theme.colors.Primary.primary500 }}>Together.</Text>
              </Text>
            </Animated.View>
            <Animated.View style={[styles.tooltipContainer, tooltipAnimatedStyle]}>
              <TouchableOpacity style={[styles.tooltip, { backgroundColor: theme.colors.Background.background50 }]} onPress={() => {
                handleContinue();
              }}>
                <HeartHandshake size={50} color={theme.colors.Text.text950} strokeWidth={2.5} />
              </TouchableOpacity>
              <Tooltip text="Press to give and receive support!" />
            </Animated.View>
          </View>

          <Animated.View style={[styles.bottomContainer, logoAnimatedStyle]}>
            <Image 
              source={require('@/assets/images/chronicareText.png')}
              style={styles.logo}
            />
          </Animated.View>
        </View>
      </SafeAreaView>
    </ImageBackgroundComponent>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomContainer: {
    alignItems: 'center',
  },
  title: {
    fontSize: 32,
    textAlign: 'center',
    marginBottom: 16,
  },
  tooltip: {
    padding: 10,
    borderRadius: 999,
    width: 72,
    height: 72,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#9FA6B2',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 3,
  },
  tooltipContainer: {
    alignItems: 'center',
    flexDirection: 'column',
    gap: 15,
  },
  logo: {
    width: 120,
    resizeMode: 'contain',
  },
});
