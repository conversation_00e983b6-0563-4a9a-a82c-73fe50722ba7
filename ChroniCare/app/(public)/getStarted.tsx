import { View, Text, SafeAreaView, StyleSheet, TouchableOpacity, Platform, Alert } from 'react-native'
import React, { useMemo, useCallback, useRef, useState, useEffect } from 'react'
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import {
  BottomSheetModal,
  BottomSheetView,
  BottomSheetModalProvider,
  BottomSheetBackdrop,
} from '@gorhom/bottom-sheet';
import { useRouter } from 'expo-router';
import { useTheme } from '@/scr/context/themeContext';
import { useAuth } from '@/scr/context/authContext';
import { useMutation } from '@apollo/client';
import { CREATE_USER } from '@/scr/graphql/queries';
import { Button } from '@/scr/components/button';
import OnboardingCarousel, { OnboardingSlide } from '@/scr/components/onboarding/OnboardingCarousel';
import { ImageBackgroundComponent } from '@/scr/components/onboarding/imageBackground';
import * as WebBrowser from 'expo-web-browser';

const getStarted = () => {
    const { theme } = useTheme();
    const router = useRouter();
    const { signInWithGoogle, loading, user } = useAuth();
    const [isSignupInProgress, setIsSignupInProgress] = useState(false);

    const [createUser] = useMutation(CREATE_USER, {
        onCompleted: (data) => {
            console.log('User created in database:', data.createUser);
        },
        onError: (error) => {
            console.error('Error creating user in database:', error);
            // Don't show error to user as Firebase signup was successful
            // The user can still use the app, database creation is supplementary
        },
    });

    // Effect to handle user creation in database after successful Firebase auth
    useEffect(() => {
        const createUserInDatabase = async () => {
            if (user && isSignupInProgress) {
                console.log('Firebase auth successful, creating user in database...');
                
                try {
                    await createUser({
                        variables: {
                            input: {
                                firebaseUid: user.uid,
                                email: user.email,
                                displayName: user.displayName,
                                emailVerified: user.emailVerified,
                                photoURL: user.photoURL,
                                onboardingCompleted: false,
                            },
                        },
                    });
                    console.log('User successfully created in database');
                } catch (dbError) {
                    console.error('Database user creation failed, but Firebase signup succeeded:', dbError);
                    // Continue with the flow as Firebase signup was successful
                } finally {
                    setIsSignupInProgress(false);
                }
            }
        };

        createUserInDatabase();
    }, [user, isSignupInProgress, createUser]);

    // Onboarding slides data
    const slides: OnboardingSlide[] = [
        {
            id: '1',
            image: require('@/assets/images/communityMockUp.png'),
            title: 'Community that understands',
            description: 'Connect with supportive people that have lived experience in your condition'
        },
        {
            id: '2',
            image: require('@/assets/images/trackingMockUp.png'),
            title: 'Track your health journey',
            description: 'Monitor symptoms, medications, and progress with easy-to-use tracking tools designed for chronic conditions'
        }
    ];

    // Bottom sheet ref
    const bottomSheetModalRef = useRef<BottomSheetModal>(null);

    // Bottom sheet callbacks
    const handlePresentModalPress = useCallback(() => {
        bottomSheetModalRef.current?.present();
    }, []);

   

    // Google sign-in handler
    const handleGoogleSignIn = useCallback(async () => {
        try {
            setIsSignupInProgress(true);
            await signInWithGoogle();
            // User creation in database will be handled by the useEffect above
            // Navigation will be handled by the auth state change in _layout.tsx
            bottomSheetModalRef.current?.dismiss();
        } catch (error) {
            setIsSignupInProgress(false);
            // Error handling is done in the auth context
            console.error('Google sign-in error:', error);
            Alert.alert('Error', 'Failed to sign in with Google. Please try again.');
        }
    }, [signInWithGoogle]);

    // Navigation function
    const navigateToLogin = useCallback(() => {
        router.push('/(public)/login');
    }, [router]);

    const navigateToTermsOfService = useCallback(() => {
        WebBrowser.openBrowserAsync('https://chronicare.io/terms-of-service');
    }, []);

    const navigateToSignup = useCallback(() => {
        router.push('/(public)/signup');
    }, [router]);

    // Bottom sheet snap points - increased height for additional content
    const snapPoints = useMemo(() => [380], []);

    // Create theme-aware StyleSheet - only recreated when theme changes
    const styles = useMemo(() => StyleSheet.create({
        container: {
            flex: 1,
            backgroundColor: 'transparent',
        },
        content: {
            flex: 1,
            justifyContent: 'space-between',
            alignItems: 'center',
        },
        centerContainer: {
            flex: 1,
            justifyContent: 'center',
            paddingTop: 40,
            width: '100%',
        },

        bottomContainer: {
            width: '100%',
            paddingBottom: 40,
            paddingHorizontal: 20,
        },
        modalContainer: {
            height: 320,
            backgroundColor: theme.colors.Background.background0,
            paddingHorizontal: 20,
            paddingTop: 20,
        },
        modalButtonContainer: {
            marginBottom: 12,
        },
        divider: {
            flexDirection: 'row',
            alignItems: 'center',
            marginVertical: 20,
        },
        dividerLine: {
            flex: 1,
            height: 1,
            backgroundColor: theme.colors.Text.text300,
        },
        footer: {
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            marginTop: 8,
            flexWrap: 'wrap',
        },
        footerText: {
            ...theme.textVariants.text('xs', 'regular'),
            color: theme.colors.Text.text700,
        },
        linkText: {
            ...theme.textVariants.text('xs', 'semibold'),
            color: theme.colors.Primary.primary500,
        },
    }), [theme]);

    // Backdrop component
    const renderBackdrop = useCallback(
        (props: any) => (
            <BottomSheetBackdrop
                {...props}
                disappearsOnIndex={-1}
                appearsOnIndex={0}
                enableTouchThrough={false}
            />
        ),
        []
    );

  return (
    <ImageBackgroundComponent style={styles.container}>
        <GestureHandlerRootView style={styles.container}>
            <BottomSheetModalProvider>
                <SafeAreaView style={styles.container}>
                    <View style={styles.content}>
                        <View style={styles.centerContainer}>
                            <OnboardingCarousel slides={slides} />
                        </View>
                        <View style={styles.bottomContainer}>
                            <Button title="Get started" onPress={handlePresentModalPress} />
                        </View>
                    </View>
                </SafeAreaView>

                <BottomSheetModal
                    ref={bottomSheetModalRef}
                    snapPoints={snapPoints}
                    enablePanDownToClose={true}
                    backgroundStyle={{ backgroundColor: theme.colors.Background.background0 }}
                    backdropComponent={renderBackdrop}
                    handleIndicatorStyle={{
                        backgroundColor: theme.colors.Background.background900,
                    }}
                >
                    <BottomSheetView style={styles.modalContainer}>
                        <View style={styles.modalButtonContainer}>
                            <Button 
                                variant="iconNeutral" 
                                icon="logo-google" 
                                title="Continue with Google" 
                                onPress={handleGoogleSignIn}
                                loading={loading}
                            />
                        </View>
                        <View style={styles.modalButtonContainer}>
                            <Button variant="iconNeutral" backgroundColor={theme.colors.Background.background700} textColor={theme.colors.Text.text100} icon="logo-apple" title="Continue with Apple (Coming Soon)" onPress={() => {}} />
                        </View>
                        <View style={styles.modalButtonContainer}>
                            <Button variant="iconNeutral" icon="mail" title="Continue with Email" onPress={navigateToSignup} />
                        </View>

                        <View style={styles.divider}>
                            <View style={styles.dividerLine} />

                        </View>

                        <View style={styles.footer}>
                            <Text style={styles.footerText}>
                                By continuing, you agree to our{' '}
                            </Text>
                            <TouchableOpacity onPress={navigateToTermsOfService}>
                                <Text style={styles.linkText}>
                                    Terms of Service
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </BottomSheetView>
                </BottomSheetModal>
            </BottomSheetModalProvider>
        </GestureHandlerRootView>
    </ImageBackgroundComponent>
  )
}

export default getStarted