import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  KeyboardAvoidingView,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  Platform,
  Alert,
} from 'react-native';
import { CircleUserRound, Eye, EyeOff } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useTheme } from '../../scr/context/themeContext';
import { useAuth } from '../../scr/context/authContext';
import { BackNextButton } from '../../scr/components/backNextButton';
import { ImageBackgroundComponent } from '../../scr/components/onboarding/imageBackground';

export default function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);
  const [emailBlurred, setEmailBlurred] = useState(false);
  const [passwordBlurred, setPasswordBlurred] = useState(false);
  const router = useRouter();
  const { theme } = useTheme();
  const { signIn, loading, forgotPassword } = useAuth();

  // Validation function
  const isFormValid = useMemo(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return (
      email.trim() !== '' &&
      emailRegex.test(email) &&
      password.length >= 6
    );
  }, [email, password]);

  // Validation errors - only show after submit attempt
  const passwordTooShort = passwordBlurred && password.length > 0 && password.length < 6;
  const emailInvalid = emailBlurred && email.length > 0 && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

  // Create theme-aware StyleSheet - only recreated when theme changes
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'transparent',
    },
    keyboardView: {
      flex: 1,
      paddingBottom: 100,
    },
    content: {
      flex: 1,
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingBottom: 30,
    },
    mainContent: {
      flex: 1,
      justifyContent: 'flex-end',
      alignItems: 'stretch',
    },
    centeredPageContentWrapper: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'stretch',
      gap: 20,
    },
    profileIcon: {
      alignSelf: 'flex-start',
    },
    title: {
      ...theme.textVariants.heading('xl', 'bold'),
      color: theme.colors.Text.text950,
      alignSelf: 'flex-start',
    },
    
    inputContainer: {
      width: '100%',
    },
    label: {
      fontSize: 16,
      color: theme.colors.Text.text950,
    },
    required: {
      color: '#ff4757',
    },
    inputWrapper: {
      position: 'relative',
      width: '100%',
    },
    input: {
      fontSize: 16,
      color: theme.colors.Text.text950,
      paddingVertical: 12,
      paddingRight: 40,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.Text.text950,
      backgroundColor: 'transparent',
    },
    eyeIcon: {
      position: 'absolute',
      right: 0,
      top: 12,
      padding: 4,
    },
    loader: {
      alignSelf: 'center',
    },
    errorText: {
      color: theme.colors.Secondary.secondary500,
      fontSize: 14,
      marginTop: 4,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 20,
    },
    footerText: {
      fontSize: 16,
      color: theme.colors.Text.text700,
    },
    linkText: {
      fontSize: 16,
      color: theme.colors.Primary.primary500,
      fontWeight: 'bold',
    },
    forgotPasswordContainer: {
      alignSelf: 'flex-start',
      marginTop: 8,
    },
    forgotPasswordText: {
      fontSize: 14,
      color: theme.colors.Primary.primary500,
    },
  }), [theme]);

  const handleSignIn = async () => {
    setHasAttemptedSubmit(true);
    
    if (!isFormValid) {
      return;
    }

    try {
      await signIn(email, password);
      // Navigation will be handled by the auth state change in _layout.tsx
    } catch (error) {
      // Error handling is done in the auth context
      console.error('Login error:', error);
    }
  };

  const handleForgotPassword = () => {
    Alert.prompt(
      'Forgot Password',
      'Please enter your email address to receive a password reset link.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Send',
          onPress: async (text) => {
            if (text) {
              try {
                await forgotPassword(text);
              } catch (error) {
                // Error is already handled in auth context, but you could add more UI feedback here if needed
                console.log('Forgot password flow completed.');
              }
            }
          },
        },
      ],
      'plain-text',
      email, // Pre-fills the prompt with the current email state
      'email-address'
    );
  };

  const handleBack = () => {
    router.push('/(public)/getStarted');
  };

  const handleNext = () => {
    handleSignIn();
  };

  const navigateToSignup = () => {
    router.push('/(public)/signup');
  };

  return (
    <ImageBackgroundComponent style={styles.container}>
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardView}
        >
          <View style={styles.content}>
            <View style={styles.mainContent}>
              <View style={styles.centeredPageContentWrapper}>
                {/* Profile Icon */}
                <CircleUserRound 
                  size={32} 
                  color={theme.colors.Text.text950}
                  style={styles.profileIcon}
                />

                {/* Title */}
                <Text style={styles.title}>Sign in to your account</Text>

                {/* Email Input */}
                <View style={styles.inputContainer}>
                  <Text style={styles.label}>
                    E-Mail<Text style={styles.required}>*</Text>
                  </Text>
                  <View style={styles.inputWrapper}>
                    <TextInput
                      style={styles.input}
                      placeholder="Enter e-mail"
                      placeholderTextColor={theme.colors.Text.text500}
                      value={email}
                      onChangeText={setEmail}
                      autoCapitalize="none"
                      keyboardType="email-address"
                      autoComplete="email"
                      onFocus={() => setEmailBlurred(false)}
                      onBlur={() => setEmailBlurred(true)}
                    />
                  </View>
                  {emailInvalid && (
                    <Text style={styles.errorText}>Please enter a valid email address</Text>
                  )}
                </View>

                {/* Password Input */}
                <View style={styles.inputContainer}>
                  <Text style={styles.label}>
                    Password<Text style={styles.required}>*</Text>
                  </Text>
                  <View style={styles.inputWrapper}>
                    <TextInput
                      style={styles.input}
                      placeholder="Enter password"
                      placeholderTextColor={theme.colors.Text.text500}
                      value={password}
                      onChangeText={setPassword}
                      secureTextEntry={!showPassword}
                      autoComplete="current-password"
                      onFocus={() => setPasswordBlurred(false)}
                      onBlur={() => setPasswordBlurred(true)}
                    />
                    <TouchableOpacity
                      style={styles.eyeIcon}
                      onPress={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <Eye size={20} color={theme.colors.Text.text600} />
                      ) : (
                        <EyeOff size={20} color={theme.colors.Text.text600} />
                      )}
                    </TouchableOpacity>
                  </View>
                  {passwordTooShort && (
                    <Text style={styles.errorText}>Password must be at least 6 characters</Text>
                  )}
                </View>

                {/* Forgot Password Link */}
                <TouchableOpacity
                  style={styles.forgotPasswordContainer}
                  onPress={handleForgotPassword}
                  disabled={loading}
                >
                  <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
                </TouchableOpacity>

                {loading && (
                  <ActivityIndicator
                    size="large"
                    color={theme.colors.Primary.primary500}
                    style={styles.loader}
                  />
                )}

                {/* Footer */}
                <View style={styles.footer}>
                  <Text style={styles.footerText}>
                    Don't have an account?{' '}
                  </Text>
                  <TouchableOpacity onPress={navigateToSignup}>
                    <Text style={styles.linkText}>
                      Sign Up
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Back/Next Buttons */}
              <BackNextButton
                onBackPress={handleBack}
                onNextPress={handleNext}
                nextInactive={!isFormValid || loading}
                disabled={loading}
              />
            </View>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ImageBackgroundComponent>
  );
}
