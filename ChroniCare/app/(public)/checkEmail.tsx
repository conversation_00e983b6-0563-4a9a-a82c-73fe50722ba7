import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  TouchableOpacity,
} from 'react-native';
import { Mail } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useTheme } from '../../scr/context/themeContext';
import { BackNextButton } from '../../scr/components/backNextButton';
import IconTitleInstructions from '../../scr/components/IconTitleInstructions';

const CheckEmail = () => {
  const router = useRouter();
  const { theme } = useTheme();

  const handleBack = () => {
    router.back();
  };

  const handleNext = () => {
    // Navigate to the next step in the onboarding process
    // This could be the login page or main app depending on your flow
    router.push('/(public)/login');
  };

  const resendEmail = () => {
    // Implement resend email functionality here
    console.log('Resending verification email...');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.Background.background0 }]}>
      <View style={styles.content}>
        {/* Icon, Title, and Instructions */}
        <IconTitleInstructions
          icon={Mail}
          title="Check your email"
          instructions="Didn't receive the email? Check your spam folder or request a new one."
        />

        {/* Description */}
        <Text style={[styles.description, { color: theme.colors.Text.text700 }]}>
          We've sent a verification link to your email address. Please check your inbox and click the link to verify your account.
        </Text>

        {/* Resend Email Button */}
        <TouchableOpacity 
          style={[styles.resendButton, { borderColor: theme.colors.Primary.primary500 }]} 
          onPress={resendEmail}
        >
          <Text style={[styles.resendButtonText, { color: theme.colors.Primary.primary500 }]}>
            Resend Email
          </Text>
        </TouchableOpacity>

        {/* Back/Next Buttons */}
        <View style={styles.buttonsContainer}>
          <BackNextButton
            onBackPress={handleBack}
            onNextPress={handleNext}
            nextTitle="Continue"
            nextInactive={false}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default CheckEmail;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 32,
    paddingTop: 80,
    paddingBottom: 32,
    alignItems: 'center',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  resendButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 60,
  },
  resendButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  buttonsContainer: {
    width: '100%',
    marginTop: 'auto',
  },
});