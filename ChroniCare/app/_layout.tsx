import { Stack, useRouter, useSegments } from 'expo-router';
import { useEffect } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { OnboardingProvider, useOnboarding } from '../scr/context/onboardingContext';
import { ThemeProvider } from '../scr/context/themeContext';
import { AuthProvider, useAuth } from '../scr/context/authContext';
import { ApolloProvider } from '../scr/providers/ApolloProvider';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { UserProvider } from '../scr/context/userContext';


function RootLayoutContent() {
	const router = useRouter();
	const segments = useSegments();
	const { isLoading: onboardingLoading, hasCompletedOnboarding } = useOnboarding();
	const { user, initializing } = useAuth();

	useEffect(() => {
		if (initializing || onboardingLoading) return;

		const inAuthGroup = segments[0] === '(auth)';
		const inOnboardingFlow = segments.some(segment => segment === '(onboarding)');

		if (user) {
			// User is authenticated.
			if (hasCompletedOnboarding) {
				// Redirect to the main app if they are not already there.
				if (!inAuthGroup || inOnboardingFlow) {
					router.replace('/(auth)/(tabs)/community');
				}
			} else {
				// User has not completed onboarding. Redirect to onboarding flow
				// if they are not already in it.
				const inOnboardingFlow = segments.some(segment => segment === '(onboarding)');
				if (!inOnboardingFlow) {
					router.replace('/(auth)/(onboarding)/consent');
				}
			}
		} else {
			// User is not authenticated.
			// Ensure they are always in the (public) group.
			const inPublicGroup = segments[0] === '(public)';
			if (!inPublicGroup) {
				if (hasCompletedOnboarding) {
					router.replace('/(public)/login');
				} else {
					router.replace('/(public)/intro');
				}
			}
		}
	}, [user, initializing, hasCompletedOnboarding, onboardingLoading, segments, router]);

	if (initializing || onboardingLoading)
		return (
			<View
				style={{
					alignItems: 'center',
					justifyContent: 'center',
					flex: 1
				}}
			>
				<ActivityIndicator size="large" />
			</View>
		);

	return (
		<Stack>
			<Stack.Screen name="index" options={{ headerShown: false }} />
			<Stack.Screen name="(public)" options={{ headerShown: false }} />
			<Stack.Screen name="(auth)" options={{ headerShown: false }} />
		</Stack>
	);
}

export default function RootLayout() {
	return (
		<GestureHandlerRootView style={{ flex: 1 }}>
			<KeyboardProvider>
				<ThemeProvider>
					<AuthProvider>
						<ApolloProvider>
							<UserProvider>
								<OnboardingProvider>
									<RootLayoutContent />
								</OnboardingProvider>
							</UserProvider>
						</ApolloProvider>
					</AuthProvider>
				</ThemeProvider>
			</KeyboardProvider>
		</GestureHandlerRootView>
	);
}