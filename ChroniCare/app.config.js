const IS_DEV = process.env.APP_VARIANT === 'development';

export default {
  expo: {
    name: IS_DEV ? 'ChroniCare (Dev)' : 'ChroniCare',
    slug: 'chronicare',
    version: '1.0.0',
    orientation: 'portrait',
    icon: './assets/images/blueIcon.png',
    scheme: 'chronicare',
    userInterfaceStyle: 'automatic',
    newArchEnabled: true,
    ios: {
      supportsTablet: true,
      bundleIdentifier: IS_DEV ? 'io.chronicare.ChroniCare.dev' : 'io.chronicare.ChroniCare',
      googleServicesFile: './GoogleService-Info.plist',
      infoPlist: {
        NSPhotoLibraryUsageDescription: 'This app needs access to your photo library to let you select a profile picture.',
        NSCameraUsageDescription: 'This app needs access to your camera to let you take a profile picture.',
        NSMicrophoneUsageDescription: 'This app needs access to your microphone for voice features.',
        CFBundleDisplayName: IS_DEV ? 'ChroniCare Dev' : 'ChroniCare',
        ITSAppUsesNonExemptEncryption: false,
      },
    },
    android: {
      adaptiveIcon: {
        foregroundImage: './assets/images/blueIcon.png',
        backgroundColor: '#ffffff',
      },
      googleServicesFile: './google-services.json',
      edgeToEdgeEnabled: true,
      package: IS_DEV ? 'io.chronicare.ChroniCare.dev' : 'io.chronicare.ChroniCare',
      permissions: [
        'android.permission.RECORD_AUDIO',
        'android.permission.CAMERA',
        'android.permission.READ_EXTERNAL_STORAGE',
        'android.permission.WRITE_EXTERNAL_STORAGE',
      ],
    },
    web: {
      bundler: 'metro',
      output: 'static',
      favicon: './assets/images/blueIcon.png',
    },
    plugins: [
      'expo-router',
      [
        'expo-splash-screen',
        {
          image: './assets/images/splash-icon.png',
          imageWidth: 200,
          resizeMode: 'contain',
          backgroundColor: '#ffffff',
        },
      ],
      'expo-font',
      'expo-web-browser',
      '@react-native-firebase/app',
      '@react-native-firebase/auth',
      [
        '@react-native-google-signin/google-signin',
        {
          iosUrlScheme: 'com.googleusercontent.apps.1056297800349-sp8htepcij1fu64i395tei5gqur401sc',
        },
      ],
      [
        'expo-build-properties',
        {
          ios: {
            useFrameworks: 'static',
          },
        },
      ],
      'expo-localization',
      [
        'expo-image-picker',
        {
          photosPermission: 'The app needs access to your photos to let you select a profile picture.',
        },
      ],
    ],
    experiments: {
      typedRoutes: true,
    },
    extra: {
      router: {},
      eas: {
        projectId: '1f5aff68-976a-455d-8121-1adc720b18a3',
      },
    },
    owner: 'alexdadi',
    updates: {
      url: 'https://u.expo.dev/1f5aff68-976a-455d-8121-1adc720b18a3',
    },
    runtimeVersion: '1.0.0',
  },
};