import * as React from "react"
import Svg, { Path } from "react-native-svg"

const chronicare = ({ color = '#262627', ...props }: { color?: string } & any) => (
  <Svg {...props} xmlns="http://www.w3.org/2000/svg" fill="none">
    <Path
      fill={color}
      d="M113.004 24.056c-1.526 0-2.806-.324-3.84-.971-1.024-.657-1.797-1.575-2.318-2.754-.522-1.178-.783-2.555-.783-4.13 0-1.275.184-2.415.551-3.42.376-1.014.893-1.874 1.55-2.58a6.747 6.747 0 0 1 2.275-1.637A6.75 6.75 0 0 1 113.178 8c1.749 0 3.116.474 4.101 1.42.995.947 1.512 2.319 1.551 4.116.01.59 0 1.082-.029 1.478a6.333 6.333 0 0 1-.145 1.014h-9.405c.01.899.106 1.74.29 2.522.193.782.473 1.464.84 2.043a4.16 4.16 0 0 0 1.421 1.362c.57.32 1.246.479 2.028.479.773 0 1.546-.136 2.319-.406.782-.28 1.386-.594 1.811-.942l.435.942c-.328.348-.778.676-1.348.985-.56.3-1.193.546-1.898.74a7.62 7.62 0 0 1-2.145.304Zm-3.753-9.1 6.478-.146c.029-.212.048-.435.058-.666.009-.242.014-.469.014-.681 0-1.314-.222-2.358-.666-3.13-.445-.774-1.189-1.16-2.232-1.16-.551 0-1.048.13-1.493.391-.444.252-.826.623-1.145 1.116-.309.483-.55 1.087-.724 1.812-.164.714-.261 1.536-.29 2.463Z"
    />
    <Path
      fill={color}
      d="m97.017 23.737-.003-1.035 1.916-.255v-11.97h-3.296s0-.024-.173-.369c-.172-.345-.517-.732-.517-.732l5.783-1.188h.434l.508.39-.015.798-.058 2.507.058-.102c.068-.26.222-.589.464-.985.241-.406.56-.807.956-1.203.396-.406.86-.744 1.391-1.014a3.843 3.843 0 0 1 1.768-.406c.232 0 .416.015.551.044.145.019 1.271.166 2.134 1.028 0 0-.555.382-1.035.863-.481.48-.863 1.208-.863 1.208-.097-.068-.144-.235-.366-.303a2.947 2.947 0 0 0-.841-.101c-.618 0-1.174.077-1.666.232a4.296 4.296 0 0 0-1.305.623c-.376.26-.7.57-.97.927v9.739l2.811.362v.942h-7.666Z"
    />
    <Path
      fill={color}
      d="M88.526 24.012a5.036 5.036 0 0 1-2.144-.463 3.866 3.866 0 0 1-1.623-1.406c-.416-.628-.624-1.416-.624-2.362 0-.937.261-1.73.783-2.377.531-.657 1.232-1.183 2.101-1.58a12.1 12.1 0 0 1 2.898-.898 17.395 17.395 0 0 1 3.145-.29v-1.318c0-.764-.082-1.396-.246-1.899-.164-.502-.46-.879-.884-1.13-.416-.251-1.01-.377-1.783-.377-1.024 0-1.937.145-2.739.435-.801.29-1.955.624-2.99 1.486v-1.486c.28-.242 1.189-.715 1.788-1.043.599-.329 1.309-.619 2.13-.87a9.023 9.023 0 0 1 2.652-.377c1.198 0 2.16.193 2.884.58.734.386 1.265.976 1.594 1.768.328.792.492 1.802.492 3.029v9.173l1.399.095v.862c-.172.173 0 .173-.345.173h-2.415c-.512 0-.784-.008-1.035-.173-.242-.164-.371-.344-.371-.856v-1.174c-.203.29-.522.633-.957 1.03-.425.386-.952.724-1.58 1.014-.627.29-1.337.434-2.13.434Zm1.203-1.739c.6 0 1.188-.145 1.768-.434a5.685 5.685 0 0 0 1.565-1.13v-4.957c-1.207 0-2.246.155-3.115.464-.87.3-1.541.724-2.015 1.275a2.846 2.846 0 0 0-.695 1.913c0 .628.11 1.154.333 1.58.222.425.522.748.898.97.387.213.807.32 1.261.32Z"
    />
    <Path
      fill={color}
      d="M79.101 24.056c-1.497 0-2.758-.334-3.782-1-1.014-.677-1.782-1.6-2.304-2.768-.522-1.179-.783-2.522-.783-4.029-.01-1.169.16-2.25.508-3.246a7.963 7.963 0 0 1 1.536-2.637 7.148 7.148 0 0 1 2.449-1.768c.956-.425 2.033-.638 3.231-.638.638 0 1.227.063 1.768.189.541.125 1.014.26 1.42.405.406.136.73.237.971.305l-.04 2.964H82.35l-.684-1.704c-.029-.154-.135-.309-.319-.463-.173-.155-.415-.285-.724-.392a3.397 3.397 0 0 0-1.102-.159c-.753 0-1.439.246-2.057.74-.609.482-1.097 1.202-1.464 2.158-.357.957-.54 2.145-.55 3.565 0 1.12.1 2.111.304 2.97.212.851.517 1.561.913 2.131.396.57.87 1.005 1.42 1.304.55.29 1.16.435 1.826.435.521 0 1.024-.058 1.507-.174.492-.125.947-.28 1.362-.464.415-.193 1.023-.61 1.293-.803.1.234.18.417.257.6.08.185.16.37.26.607-.344.234-1.034.518-1.622.828-.54.3-1.144.546-1.811.74a7.031 7.031 0 0 1-2.058.304Z"
    />
    <Path
      fill={color}
      d="M66.264 23.737v-.957l2-.362v-11.94l-3.338.32s-.172-.518-.345-.863c-.172-.345-.69-.862-.69-.862l6.43-.885h.348l.55.434v13.81l2 .348v.957h-6.955Zm3.376-17.81c-.54 0-.97-.17-1.29-.507-.309-.348-.463-.797-.463-1.348 0-.59.178-1.087.536-1.493.357-.415.84-.623 1.45-.623h.028c.531 0 .952.17 1.26.507.32.329.479.769.479 1.32 0 .589-.179 1.096-.536 1.52-.358.416-.836.624-1.435.624h-.029Z"
    />
    <Path
      fill={color}
      d="M52.207 22.418v-11.94l-2.462.217s0-.07-.345-.587c-.345-.518-.69-.69-.69-.69l5.381-1.23h.435l.493.39v1.19l-.015.927c.377-.406.85-.807 1.42-1.203a9.02 9.02 0 0 1 1.913-1.015 6.06 6.06 0 0 1 2.188-.405c1.14 0 2.03.232 2.667.695.637.464 1.087 1.155 1.348 2.072.26.909.39 2.039.39 3.391v8.203l1.798.333v.97h-6.463v-.956l1.695-.347v-8.16c0-.926-.068-1.709-.203-2.347-.135-.647-.396-1.135-.782-1.463-.387-.329-.952-.493-1.696-.493a5.52 5.52 0 0 0-1.565.217 6.126 6.126 0 0 0-1.362.595c-.415.241-.807.507-1.174.797v10.84l1.797.361v.957h-6.536v-.957l1.768-.362Z"
    />
    <Path
      fill={color}
      d="M36.443 16.158c0-1.333.203-2.507.608-3.522.416-1.014.971-1.864 1.667-2.55a7.101 7.101 0 0 1 2.362-1.565 6.989 6.989 0 0 1 2.681-.536c1.604 0 2.913.352 3.927 1.058 1.014.695 1.763 1.642 2.246 2.84.483 1.188.725 2.521.725 4 0 1.333-.208 2.511-.623 3.535-.406 1.015-.957 1.87-1.652 2.565a7.05 7.05 0 0 1-2.348 1.55 7.194 7.194 0 0 1-2.695.523c-1.188 0-2.222-.198-3.101-.595a5.783 5.783 0 0 1-2.145-1.68 7.34 7.34 0 0 1-1.246-2.522c-.27-.956-.406-1.99-.406-3.101Zm7.13 6.724c.801 0 1.483-.247 2.043-.74.57-.502 1-1.245 1.29-2.23.3-.996.449-2.237.449-3.725 0-.947-.068-1.84-.203-2.681-.135-.85-.353-1.599-.652-2.246-.29-.657-.676-1.17-1.16-1.536-.483-.377-1.072-.565-1.767-.565-.812 0-1.508.246-2.087.739-.57.492-1.005 1.236-1.304 2.231-.3.986-.45 2.227-.45 3.725 0 .956.068 1.86.203 2.71.145.84.367 1.589.667 2.246.309.647.705 1.154 1.188 1.521s1.077.55 1.783.55Z"
    />
    <Path
      fill={color}
      d="M27.38 23.737v-.957l1.913-.333v-11.97h-3.552s-.19-.481-.321-.714c-.13-.233-.345-.518-.345-.518l6.015-1.057h.435l.507.39-.014.798-.058 2.507.058-.102c.067-.26.222-.589.463-.985.242-.406.56-.807.957-1.203.396-.406.86-.744 1.391-1.014a3.84 3.84 0 0 1 1.768-.406c.232 0 .415.015.55.044.145.019 1.212.166 1.73 1.159l-.863.905-.69.862c-.097-.068-.085-.062-.307-.13a2.939 2.939 0 0 0-.84-.101c-.619 0-1.174.077-1.667.232a4.292 4.292 0 0 0-1.304.623c-.377.26-.7.57-.97.927v9.739l2.81.362v.942h-7.665Z"
    />
    <Path
      fill={color}
      d="M13.439 22.433V1.869l-2.145-.347V.696L15.496 0h.348l.522.391v7.855l-.087 2.42c.348-.387.802-.778 1.362-1.174a8.684 8.684 0 0 1 1.899-1.015 6.06 6.06 0 0 1 2.188-.405c1.15 0 2.038.236 2.666.71.638.463 1.082 1.154 1.333 2.072.252.908.377 2.043.377 3.405v8.16l1.826.361v.957h-6.492v-.957l1.71-.362v-8.13c0-.927-.063-1.714-.189-2.362-.125-.647-.381-1.135-.768-1.463-.376-.329-.946-.493-1.71-.493-.53 0-1.038.072-1.521.217a5.945 5.945 0 0 0-1.377.58c-.425.242-.826.507-1.203.797v10.854l1.84.362v.957h-6.55v-.957l1.769-.347Z"
    />
    <Path
      fill={color}
      d="M7.036 24.056c-1.497 0-2.758-.334-3.782-1-1.015-.677-1.783-1.6-2.304-2.768-.522-1.179-.783-2.522-.783-4.029-.01-1.169.16-2.25.507-3.246a7.962 7.962 0 0 1 1.536-2.637 7.147 7.147 0 0 1 2.45-1.768c.956-.425 2.033-.638 3.231-.638.638 0 1.227.063 1.768.189.541.125 1.014.26 1.42.405.406.136 1.678.643 1.92.71V13.042h-2.775l-.623-2.913c-.029-.154-.135-.309-.319-.463-.174-.155-.415-.285-.724-.392a3.397 3.397 0 0 0-1.102-.159c-.753 0-1.44.246-2.057.74-.61.482-1.097 1.202-1.464 2.158-.358.957-.541 2.145-.55 3.565 0 1.12.1 2.111.304 2.97.212.851.516 1.561.912 2.131.397.57.87 1.005 1.42 1.304.551.29 1.16.435 1.827.435.521 0 1.024-.058 1.507-.174.492-.125.946-.28 1.362-.464.415-.193 2.184-1.128 2.454-1.321v1.035c-.309.319-1.725 1.21-2.266 1.518-.54.3-1.145.546-1.811.74a7.031 7.031 0 0 1-2.058.304Z"
    />
  </Svg>
)
export default chronicare
