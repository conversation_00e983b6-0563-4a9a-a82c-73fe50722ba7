import * as React from "react"
import Svg, { G, Path, Defs, LinearGradient, Stop, SvgProps } from "react-native-svg"
/* SVGR has dropped some elements not supported by react-native-svg: filter */

export const gradientBackground = (props: SvgProps) => {
  return (
    <Svg
      width={852}
      height={819}
      viewBox="0 0 852 819"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
        <G filter="url(#filter0_f_2758_1201)">
        <Path d="M658.587 593.873C577.247 633.395 246.702 543.172 208.317 472.236C169.931 401.3 438.239 376.512 519.579 336.99C600.919 297.468 697.975 322.935 736.361 393.871C774.747 464.807 739.926 554.352 658.587 593.873Z" 
        fill="url(#paint0_linear_2758_1201)"/>
        </G>
        <G filter="url(#filter1_f_2758_1201)">
        <Path d="M453.531 394.836C453.531 481.454 217.986 719 133.858 719C49.7297 719 148.876 481.454 148.876 394.836C148.876 308.218 217.076 238 301.204 238C385.332 238 453.531 308.218 453.531 394.836Z" fill="url(#paint1_linear_2758_1201)"/>
        </G>
        <G filter="url(#filter2_fg_2758_1201)">
        <Path d="M713.326 339.548C713.326 417.676 761.88 454.389 648.829 503.523C559.693 542.262 516.213 396.975 448.092 339.548C228.513 238.214 313.541 76 422.004 76C530.466 76 713.326 261.42 713.326 339.548Z" fill="url(#paint2_linear_2758_1201)"/>
        </G>
      <Defs>
        <LinearGradient
          id="paint0_linear_2758_1201"
          x1={725.211}
          y1={373.265}
          x2={283.967}
          y2={612.036}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#4361EE" />
          <Stop offset={1} stopColor="#F9E3D2" />
        </LinearGradient>
        <LinearGradient
          id="paint1_linear_2758_1201"
          x1={276.766}
          y1={238}
          x2={276.766}
          y2={719}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#B0BEFF" />
          <Stop offset={0.807692} stopColor="#F1F3FE" />
        </LinearGradient>
        <LinearGradient
          id="paint2_linear_2758_1201"
          x1={367.652}
          y1={106.218}
          x2={555.769}
          y2={534.274}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#FF81AD" />
          <Stop offset={0.706731} stopColor="#FBE9EF" />
        </LinearGradient>
      </Defs>
    </Svg>
  )
}

