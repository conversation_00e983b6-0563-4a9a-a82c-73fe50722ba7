<svg width="852" height="819" viewBox="0 0 852 819" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_2758_1201)">
<path d="M658.587 593.873C577.247 633.395 246.702 543.172 208.317 472.236C169.931 401.3 438.239 376.512 519.579 336.99C600.919 297.468 697.975 322.935 736.361 393.871C774.747 464.807 739.926 554.352 658.587 593.873Z" fill="url(#paint0_linear_2758_1201)"/>
</g>
<g filter="url(#filter1_f_2758_1201)">
<path d="M453.531 394.836C453.531 481.454 217.986 719 133.858 719C49.7297 719 148.876 481.454 148.876 394.836C148.876 308.218 217.076 238 301.204 238C385.332 238 453.531 308.218 453.531 394.836Z" fill="url(#paint1_linear_2758_1201)"/>
</g>
<g filter="url(#filter2_fg_2758_1201)">
<path d="M713.326 339.548C713.326 417.676 761.88 454.389 648.829 503.523C559.693 542.262 516.213 396.975 448.092 339.548C228.513 238.214 313.541 76 422.004 76C530.466 76 713.326 261.42 713.326 339.548Z" fill="url(#paint2_linear_2758_1201)"/>
</g>
<defs>
<filter id="filter0_f_2758_1201" x="104.582" y="218.374" width="747.394" height="485.139" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_2758_1201"/>
</filter>
<filter id="filter1_f_2758_1201" x="0" y="138" width="553.531" height="681" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_2758_1201"/>
</filter>
<filter id="filter2_fg_2758_1201" x="211.063" y="-24" width="612.629" height="634" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_2758_1201"/>
<feTurbulence type="fractalNoise" baseFrequency="0.20408162474632263 0.20408162474632263" numOctaves="3" seed="3663" />
<feDisplacementMap in="effect1_foregroundBlur_2758_1201" scale="8" xChannelSelector="R" yChannelSelector="G" result="displacedImage" width="100%" height="100%" />
<feMerge result="effect2_texture_2758_1201">
<feMergeNode in="displacedImage"/>
</feMerge>
</filter>
<linearGradient id="paint0_linear_2758_1201" x1="725.211" y1="373.265" x2="283.967" y2="612.036" gradientUnits="userSpaceOnUse">
<stop stop-color="#4361EE"/>
<stop offset="1" stop-color="#F9E3D2"/>
</linearGradient>
<linearGradient id="paint1_linear_2758_1201" x1="276.766" y1="238" x2="276.766" y2="719" gradientUnits="userSpaceOnUse">
<stop stop-color="#B0BEFF"/>
<stop offset="0.807692" stop-color="#F1F3FE"/>
</linearGradient>
<linearGradient id="paint2_linear_2758_1201" x1="367.652" y1="106.218" x2="555.769" y2="534.274" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF81AD"/>
<stop offset="0.706731" stop-color="#FBE9EF"/>
</linearGradient>
</defs>
</svg>
