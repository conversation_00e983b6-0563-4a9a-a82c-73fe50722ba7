---
description: UI/UX and frontend rules. Read everytime you are working on frontend files in the ChroniCare folder.
globs: 
alwaysApply: false
---

# ChroniCare Frontend Development Guide

## Theme System

### Core Principle
**NEVER hardcode colors, spacing, or typography values.** Always use the theme system.

### Theme Usage
```typescript
import { useTheme } from '../context/themeContext';

const { theme } = useTheme();
```

### Color Usage
- **Primary Colors**: Use `theme.colors.Background.background0` for main brand elements
- **Text Colors**: Use `theme.colors.Text.text900` for primary text, `theme.colors.Text.text700` for secondary
- **Background Colors**: Use `theme.colors.Background.background0` for main backgrounds
- **Error States**: Use `theme.colors.Secondary.secondary500` for errors
- **Success States**: Use `theme.colors.Success.success500` for success messages

### Icon usage
- ALWAYS use 'lucide-react-native' when using icons. 

### Typography Usage
```typescript
// For headings
...theme.textVariants.heading('xl', 'bold')

// For body text
...theme.textVariants.text('md', 'regular')

### Spacing Usage
```typescript
// Use predefined spacing values
paddingHorizontal: theme.spacing.spacing.s4, // 16px
marginTop: theme.spacing.spacing.s2, // 8px
borderRadius: theme.spacing.borderRadius.xl2, // 16px
```

## Component Structure

### Standard Component Template
```typescript
import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../context/themeContext';

export default function ComponentName() {
  const { theme } = useTheme();
  
  // State declarations
  const [state, setState] = useState('');
  
  // Memoized computed values
  const computedValue = useMemo(() => {
    // computation logic
  }, [dependencies]);
  
  // Memoized styles
  const styles = useMemo(() => StyleSheet.create({
    container: {
      backgroundColor: theme.colors.Background.background0,
      // other theme-based styles
    },
  }), [theme]);
  
  return (
    <View style={styles.container}>
      {/* Component content */}
    </View>
  );
}
```

### Required Imports Pattern
```typescript
// React and React Native
import React, { useState, useMemo } from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';

// Navigation
import { useRouter } from 'expo-router';

// Theme
import { useTheme } from '../context/themeContext';

// Components (from most general to most specific)
import { Button } from '../components/button';
```

## Styling Conventions

### StyleSheet Creation
- **ALWAYS** use `useMemo` for StyleSheet creation
- **ALWAYS** depend on `[theme]` in the dependency array
- **NEVER** create styles outside of the useMemo

```typescript
const styles = useMemo(() => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.Background.background0,
  },
  title: {
    ...theme.textVariants.heading('xl', 'bold'),
    color: theme.colors.Text.text950,
  },
}), [theme]);
```

## Performance Optimization

### Memoization Requirements
- **ALWAYS** use `useMemo` for:
  - StyleSheet creation
  - Complex computations
  - Form validation logic
  - Derived state

```typescript
// Good
const styles = useMemo(() => StyleSheet.create({...}), [theme]);
const isValid = useMemo(() => validateForm(), [formData]);

// Bad
const styles = StyleSheet.create({...}); // Will recreate on every render
```

### Callback Optimization
```typescript
// Use useCallback for event handlers passed to children
const handlePress = useCallback(() => {
  // handler logic
}, [dependencies]);
```

## Accessibility

### Required Accessibility Props
```typescript
<TouchableOpacity
  accessibilityRole="button"
  accessibilityLabel="Button description"
  accessibilityState={{ disabled: isDisabled }}
>
```

## TypeScript Usage

### Strict Typing Requirements
- **NEVER** use `any` type
- **ALWAYS** type component props
- **ALWAYS** type state and handlers

```typescript
interface ComponentProps {
  title: string;
  onPress: () => void;
  disabled?: boolean;
}

const Component: React.FC<ComponentProps> = ({ title, onPress, disabled = false }) => {
  // component logic
};
```


## Common Patterns

### Safe Area Usage
```typescript
import { SafeAreaView } from 'react-native';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

### Navigation Pattern
```typescript
import { useRouter } from 'expo-router';

const router = useRouter();

const handleNavigation = () => {
  router.push('/path');
  // or router.back();
};
```




---

## Key Reminders

1. **Theme First**: Every style should use theme values
2. **Performance**: Always memoize styles and computations
3. **Accessibility**: Include proper accessibility props
4. **TypeScript**: Maintain strict typing
5. **Consistency**: Follow established patterns throughout the codebase
8. **Safe Areas**: Use SafeAreaView for proper screen boundaries

Always use best practices and make sure to keep the code minimal and clean. Don't over complicate your implementation and write efficient, effective code. 
