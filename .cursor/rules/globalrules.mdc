---
description: 
globs: 
alwaysApply: true
---
## Overall Project Structure

*   **Monorepo:** Structured with separate `ChroniCare` (frontend) and `backend` directories.
*   **Language:** Primarily **TypeScript** for both frontend and backend.

## API style 
1. **Use GraphQL When:**
Use GraphQL for all frontend-to-backend communication involving dynamic data, nested relationships, or personalized queries.

## Frontend (`ChroniCare/`)

*   **Framework:** **React Native** managed with **Expo** SDK 53.
*   **Styling:** Always use the themeContext and theme.ts for styling when designing components and screens. 
*   **Routing:** **Expo Router** (`expo-router`).
*   **GraphQL Client:** Use **Apollo Client** for more advanced caching or state management needs and communication between backend and frontend. 
*   **Authentication:** **Firebase Authentication** (`@react-native-firebase`, `amazon-cognito-identity-js`).
*   **State Management:** Primarily handled by component state, context API, and the caching capabilities of Apollo Client.

## Backend (`backend/`)

*   **Framework:** **NestJS** (`@nestjs/core`, `@nestjs/platform-express`).
*   **API Implementation:** **GraphQL** using `@nestjs/graphql` and `@nestjs/apollo`.
*   **Database:** **PostgreSQL** 
*   **ORM:** **Prisma** (`@prisma/client`, `prisma`) for database interaction. 
*   **Authentication:** JWT-based authentication using **Passport.js** (`@nestjs/passport`, `passport`, `passport-jwt`). Use guards (`@UseGuards`) to protect resolvers.
*   **Configuration:** Use `@nestjs/config` for environment variables and configuration management.
*   **Language:** **TypeScript**.

## General Guidelines for AI

*   **Prioritize TypeScript:** All code suggestions should be in TypeScript.
*   **Auth Flow:** Assume standard Firebase JWT flow handled by Firebase Auth on the client and validated by Passport on the server.
*   **Database Interactions:** Use Prisma Client for all backend database operations.
*   **API Communication:** Frontend fetches data via GraphQL queries/mutations using or Apollo Client. Backend exposes data via NestJS GraphQL resolvers.
*   **Code Style:** Follow standard NestJS (backend) and React Native/Expo (frontend) conventions. Adhere to configured ESLint/Prettier rules if possible.
*   **Data Privacy** Always prioritise data privacy and security, and aim to have your solutions GDPR compliant. 